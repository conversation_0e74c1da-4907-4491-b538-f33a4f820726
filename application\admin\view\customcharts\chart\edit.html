<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Chart_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[chart_type]', $chartTypeList, $row['chart_type'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type_total')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[type_total]', $typeTotalList, $row['type_total'])}
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-name" data-rule="required" class="form-control selectpicker" name="row[name]" data-live-search="true" data-show-subtext="true">
                {foreach name="tableList" item="vo"}
                <option data-subtext="{$vo.TABLE_COMMENT}" value="{$vo.TABLE_NAME}" {in name="vo.TABLE_NAME" value="$row.name"}selected{/in}>{$vo.TABLE_NAME}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Field_total')}:</label>
        <div class="col-xs-12 col-sm-6">
            <select id="c-field_total" style="height:30px;" name="row[field_total]" class="form-control selectpicker" data-value="{$row.field_total|htmlentities}" data-live-search="true" data-show-subtext="true"></select>
        </div>
        <div class="col-xs-12 col-sm-2">
            <span class="text-muted"><input type="checkbox" name="row[is_distinct]" id="is_distinct" {if $row.is_distinct == 'DISTINCT'}checked{/if} value="DISTINCT"/> <label for="is_distinct"><span>去重统计</span></label></span>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Field_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-field_time" style="height:30px;" name="row[field_time]" class="form-control selectpicker" data-value="{$row.field_time|htmlentities}" data-live-search="true" data-show-subtext="true"></select>
            <input type="hidden" name="row[field_time_type]" value="{$row.field_time_type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Group_field')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-group_field" style="height:30px;" name="row[group_field]" class="form-control selectpicker" data-value="{$row.group_field|htmlentities}" data-live-search="true" data-show-subtext="true"></select>
        </div>
    </div>

    <div class="form-group chart_type">
        <label class="control-label col-xs-12 col-sm-2">{:__('选择关联表')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-join_table" class="form-control selectpicker" name="row[join_table]" data-live-search="true" data-show-subtext="true">
                <option value="">不关联</option>
                {foreach name="tableList" item="vo"}
                <option data-subtext="{$vo.TABLE_COMMENT}" value="{$vo.TABLE_NAME}" {in name="vo.TABLE_NAME" value="$row.join_table"}selected{/in}>{$vo.TABLE_NAME}</option>
                {/foreach}
            </select>
        </div>
    </div>

    <div class="form-group join_table">
        <label class="control-label col-xs-12 col-sm-2">{:__('关联外键')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-foreign_key" style="height:30px;" name="row[foreign_key]" class="form-control selectpicker" data-value="{$row.foreign_key|htmlentities}" data-live-search="true" data-show-subtext="true"></select>
        </div>
    </div>

    <div class="form-group join_table">
        <label class="control-label col-xs-12 col-sm-2">{:__('关联主键')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-local_key" style="height:30px;" name="row[local_key]" class="form-control selectpicker" data-value="{$row.local_key|htmlentities}" data-live-search="true" data-show-subtext="true"></select>
        </div>
    </div>

    <div class="form-group join_table">
        <label class="control-label col-xs-12 col-sm-2">{:__('显示的字段')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-field_show" style="height:30px;" name="row[field_show]" class="form-control selectpicker" data-value="{$row.field_show|htmlentities}" data-live-search="true" data-show-subtext="true"></select>
        </div>
    </div>

    <div class="form-group dictionary">
        <label class="control-label col-xs-12 col-sm-2">{:__('分组字典')}:</label>
        <div class="col-xs-12 col-sm-8">
            <dl class="fieldlist" data-name="row[dictionary]">
                <dd>
                    <ins>分组字段值</ins>
                    <ins>显示的名称</ins>
                </dd>
                <dd>
                    <a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a>
                </dd>
                <textarea name="row[dictionary]" class="form-control hide" cols="30" rows="5">{$row.dictionary|htmlentities}</textarea>
            </dl>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Where')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-where" class="form-control" name="row[where]" placeholder="如：status='normal' 或 id>10" type="text" value="{$row.where|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" placeholder="如：男女比例统计" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Subtext')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-subtext" class="form-control" placeholder="如：分析所有会员性别占比" name="row[subtext]" type="text" value="{$row.subtext|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Legend_title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-legend_title" data-rule="required" placeholder="如：性别" class="form-control" name="row[legend_title]" type="text" value="{$row.legend_title|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Unit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unit" data-rule="required" placeholder="如：人数" class="form-control" name="row[unit]" type="text" value="{$row.unit|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" data-rule="required" class="form-control" name="row[weigh]" type="number" value="{$row.weigh|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>