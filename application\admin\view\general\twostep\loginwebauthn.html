{include file="common/meta" /}
<style>
    .user-section {
        background: #fff;
        padding: 15px;
        margin-bottom: 20px;
        -webkit-border-radius: 4px;
        -webkit-background-clip: padding-box;
        -moz-border-radius: 4px;
        -moz-background-clip: padding;
        border-radius: 4px;
        background-clip: padding-box;
        border: 1px solid #e4ecf3;
    }

    .login-section {
        margin: 50px auto;
        width: 640px;
        -webkit-border-radius: 0;
        -webkit-background-clip: padding-box;
        -moz-border-radius: 0;
        -moz-background-clip: padding;
        border-radius: 0;
        background-clip: padding-box;
    }

    .login-section.login-section-weixin {
        min-height: 315px;
    }

    .login-section .logon-tab {
        margin: -15px -15px 0 -15px;
    }

    .login-section .logon-tab > a {
        display: block;
        padding: 20px;
        float: left;
        width: 50%;
        font-size: 16px;
        text-align: center;
        color: #616161;
        background-color: #f5f5f5;
        -webkit-transition: all 0.3s ease;
        -moz-transition: all 0.3s ease;
        -o-transition: all 0.3s ease;
        transition: all 0.3s ease;
    }

    .login-section .logon-tab > a:hover {
        background-color: #fafafa;
        -webkit-transition: all 0.3s ease;
        -moz-transition: all 0.3s ease;
        -o-transition: all 0.3s ease;
        transition: all 0.3s ease;
    }

    .login-section .logon-tab > a.active {
        background-color: #fff;
        -webkit-transition: all 0.3s ease;
        -moz-transition: all 0.3s ease;
        -o-transition: all 0.3s ease;
        transition: all 0.3s ease;
    }

    .login-section .login-main {
        padding: 40px 45px 20px 45px;
    }

    .login-section .control-label {
        font-size: 13px;
    }

    .login-section .n-bootstrap .form-group {
        position: relative;
    }

    .login-section .n-bootstrap .input-group {
        position: inherit;
    }

    .login-section .n-bootstrap .n-right {
        margin-top: 0;
        top: 0;
        position: absolute;
        left: 0;
        text-align: right;
        width: 100%;
    }

    .login-section {
        position: relative;
    }
</style>
<script src="__CDN__/assets/addons/twostep/js/webauthnauthenticate.js"></script>
<div class="container">
    <div class="user-section login-section">
        <div class="logon-tab clearfix"></div>
        <div class="login-main">
            <h1>
                <span>安全密钥</span>
                {if $other}
                <button class='btn btn-info pull-right'><a class="btn btn-info pull-right" href="{:url('general/twostep/logintotp')}" role="button">切换为 动态口令</a></button>
                {/if}
            </h1>
            <form name="form" id="form" class="form-vertical" method="POST" action="{:url('general/twostep/logincheck')}">
                {:token()}
                <div class="form-group">
                    <label class="control-label" for="twostep_code"></label>
                    <div class="alert alert-info-light">
                        硬件连接中 硬件灯光开始闪烁以后 30秒内按下按键即可</a>
                    </div>
                    <p><img class="profile-img-card" src="__CDN__/assets/addons/twostep/images/u2floading.gif" width="350px"></p>
                    <p>按下安全密钥设备上的按钮，进行登录...</p>
                    <div class="controls">
                        <input type="hidden" name="type" value="webauthn"/>
                        <input type="hidden" name="authenticate" id="authenticate"/>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    var j = {$j};
    setTimeout(function () {
        webauthnAuthenticate(j.challenge, function (success, info) {
            if (success) {
                var form = document.getElementById('form');
                var auth = document.getElementById('authenticate');
                auth.value = JSON.stringify(info);
                form.submit();
            } else {
                if (info.includes("InvalidStateError: An attempt was made to use an object that is not, or is no longer, usable")) {
                    alert("您使用的安全密钥尚未在此网站注册");
                } else {
                    alert("连接安全密钥错误: " + info);
                }
                return;
            }
        });
    }, 1000);
</script>