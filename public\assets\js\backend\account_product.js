define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'account_product/index' + location.search,
                    add_url: 'account_product/add' + location.search,
                    edit_url: 'account_product/edit',
                    del_url: 'account_product/del',
                    multi_url: 'account_product/multi',
                    import_url: 'account_product/import',
                    table: 'account_product',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                pageSize: 50,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'price', title: __('Price'), operate: 'BETWEEN', formatter: function(value, row, index) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'product_url', title: __('Product_url'), operate: 'LIKE', formatter: function(value, row, index) {
                            return '<a href="' + value + '" target="_blank">' + value + '</a>';
                        }},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 批量添加按钮事件
            $(".btn-batchadd").on('click', function () {
                var account_id = Fast.api.query('account_id');
                Fast.api.open('account_product/batchadd?account_id=' + account_id, '批量添加商品', {
                    area: ['800px', '600px'],
                    callback: function(data) {
                        // 批量添加成功后刷新当前页面的表格
                        $("#table").bootstrapTable('refresh');
                    }
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});



