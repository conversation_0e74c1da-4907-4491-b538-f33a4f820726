<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="content-type" content="text/html; charset=utf-8" />
        <title>收银台</title>
        
    </head>
    <body>
        <script>
        function getParam(name) {  
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");  
        　　//search,查询？后面的参数，并匹配正则
            var r = location.search.substr(1).match(reg);  
        　　if (r != null) return decodeURI(decodeURI(r[2])); 
        }

        let data = getParam("data");
        let param = window.btoa(data);
        document.addEventListener("AlipayJSBridgeReady", function () {
            AlipayJSBridge.call('tradePay', { orderStr: param }, function(result){
                if(result.resultCode == 9000) {
                    alert("支付成功");
                }
            });
        }, false);
    </script>
    </body>
</html>