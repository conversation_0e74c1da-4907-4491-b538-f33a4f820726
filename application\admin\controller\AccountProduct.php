<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 账号商品管理
 */
class AccountProduct extends Backend
{
    /**
     * AccountProduct模型对象
     * @var \app\admin\model\AccountProduct
     */
    protected $model = null;
    
    protected $dataLimit = "auth";
    protected $dataLimitField = "admin_id";

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\AccountProduct;
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自定义
     * 如果需要自定义必须将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        $account_id = $this->request->get('account_id');
        if (!$account_id) {
            $this->error('账号ID不能为空');
        }

        // 验证账号是否存在且为点单码账号
        $account = \app\admin\model\Account::where('id', $account_id)
            ->where('channel', '9008')
            ->find();
        if (!$account) {
            $this->error('账号不存在或不是点单码账号');
        }

        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            // 手动获取参数
            $search = $this->request->get("search", '');
            $filter = $this->request->get("filter", '');
            $op = $this->request->get("op", '', 'trim');
            $sort = $this->request->get("sort", 'id');
            $order = $this->request->get("order", "DESC");
            $offset = $this->request->get("offset/d", 0);
            $limit = $this->request->get("limit/d", 999999);

            // 计算当前页码
            $page = $limit ? intval($offset / $limit) + 1 : 1;

            // 构建查询条件
            $where = [];
            $where['account_id'] = $account_id;

            // 处理过滤条件
            if ($filter) {
                $filterArr = json_decode($filter, true);
                if ($filterArr) {
                    $where = array_merge($where, $filterArr);
                }
            }

            $list = $this->model
                ->with(['account'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit, false, ['page' => $page]);

            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }

        $this->assign('account', $account);
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        $account_id = $this->request->get('account_id');
        if (!$account_id) {
            $this->error('账号ID不能为空');
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            $params['account_id'] = $account_id;
            
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $this->assign('account_id', $account_id);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 批量添加
     */
    public function batchadd()
    {
        $account_id = $this->request->get('account_id');
        if (!$account_id) {
            $this->error('账号ID不能为空');
        }

        if ($this->request->isPost()) {
            $batch_data = $this->request->post('batch_data');
            if (empty($batch_data)) {
                $this->error('批量数据不能为空');
            }

            $lines = explode("\n", trim($batch_data));
            $success_count = 0;
            $error_messages = [];

            Db::startTrans();
            try {
                foreach ($lines as $line_num => $line) {
                    $line = trim($line);
                    if (empty($line)) continue;

                    // 解析每行数据：链接 价格
                    $parts = preg_split('/\s+/', $line, 2);
                    if (count($parts) != 2) {
                        $error_messages[] = "第" . ($line_num + 1) . "行格式错误：{$line}";
                        continue;
                    }

                    $product_url = trim($parts[0]);
                    $price = trim($parts[1]);

                    // 验证URL格式
                    if (!filter_var($product_url, FILTER_VALIDATE_URL)) {
                        $error_messages[] = "第" . ($line_num + 1) . "行链接格式错误：{$product_url}";
                        continue;
                    }

                    // 验证价格格式
                    if (!is_numeric($price) || $price <= 0) {
                        $error_messages[] = "第" . ($line_num + 1) . "行价格格式错误：{$price}";
                        continue;
                    }

                    // 检查是否已存在相同价格的商品
                    $exists = $this->model->where('account_id', $account_id)
                        ->where('price', $price)
                        ->find();
                    if ($exists) {
                        $error_messages[] = "第" . ($line_num + 1) . "行价格{$price}已存在";
                        continue;
                    }

                    // 添加商品
                    $params = [
                        'account_id' => $account_id,
                        'price' => $price,
                        'product_url' => $product_url,
                        'admin_id' => $this->auth->id
                    ];

                    $result = $this->model->allowField(true)->save($params);
                    if ($result) {
                        $success_count++;
                        $this->model = new \app\admin\model\AccountProduct; // 重新实例化
                    }
                }

                if (!empty($error_messages)) {
                    Db::rollback();
                    $this->error("批量添加失败：\n" . implode("\n", $error_messages));
                } else {
                    Db::commit();
                    $this->success("成功添加 {$success_count} 个商品", '', '', true);
                }
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
        }

        $this->assign('account_id', $account_id);
        return $this->view->fetch();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }
}









