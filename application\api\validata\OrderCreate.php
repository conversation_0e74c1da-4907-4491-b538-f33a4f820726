<?php

namespace app\api\validata;

use think\Db;
use think\Validate;


class OrderCreate extends Validate
{
    protected $rule = [
        'out_trade_no'  => 'require|unique:order,out_trade_no',
        'merchants_code'  => ['require','checkMerchants'],
        'amount'   => ['float','between:1,20000','checkAmount'],
        'channel_code' => ['require','checkChannel'],
        'notify_url' => 'require',
        'sign' => ['require','checkSign']
    ];

    protected $msg = [
        'out_trade_no.require' => '订单号不能为空',
        'merchants_code.require' => '商户编码不能为空',
        'amount.require' => '金额必须',
        'channel_code.require' => '通道编码不能为空',
        'notify_url.require' => '回调地址不能为空',
        'sign.require' => '签名不能为空',
        'amount.float'   => '金额必须是数字',
        'amount.between'  => '金额只能在1-2000之间'
    ];

    public function checkAmount($value,$rule,$data,$field)
    {
//        $merchants = DB::name("merchants")->where('code', $data['merchants_code'])->find();
//        $tenant = DB::name("admin")->where('id', $merchants['admin_id'])->find();
//        if ($data['channel_code']==8001) {
//            if ($value>$tenant['aweme_gold']) {
//                return '租户额度不足';
//            }
//        }
        return true;
    }

    public function checkMerchants($value) {
        $merchants = DB::name("merchants")->where('code', $value)->find();
        if (!$merchants) {
            return '商户不存在';
        }
        if ($merchants['status']=='hidden') {
            return '商户未启用';
        }
//        $tenant = DB::name("admin")->where('id', $merchants['admin_id'])->find();
//        if (!$tenant) {
//            return '租户不存在';
//        }
//        if ($tenant['status']=='hidden') {
//            return '租户未启用';
//        }
        return true; // 返回true表示验证通过，false则表示验证失败
    }

    public function checkChannel($value) {
        $channel = DB::name("channel")->where('code', $value)->find();
        if (!$channel) {
            return '通道不存在';
        }
        if ($channel['status']==0) {
            return '通道未启用';
        }
        return true; // 返回true表示验证通过，false则表示验证失败
    }

    public function checkSign($value,$rule,$data,$field)
    {
        $merchants = DB::name("merchants")->where('code', $data['merchants_code'])->find();
        $sign = md5(urldecode(\app\common\library\Order::ascii($data)) . "&key=" . $merchants['key']);
        if ($sign != $value) {
            return '签名错误';
        }
        return true;
    }
}