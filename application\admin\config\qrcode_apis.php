<?php
/**
 * 二维码解析API配置文件
 * 帅哥：这里配置了多个免费的二维码解析API，按优先级排序
 */

return [
    // 免费API列表，按优先级排序
    'apis' => [
        // API 1: 免费在线API (无需key)
        [
            'name' => 'QR-API免费服务',
            'url' => 'https://api.qr-api.com/decode',
            'method' => 'POST',
            'data_key' => 'url',
            'parser' => function($response) {
                $result = json_decode($response, true);
                if (isset($result['success']) && $result['success'] && isset($result['data'])) {
                    return $result['data'];
                }
                return false;
            }
        ],
        
        // API 2: 另一个免费API
        [
            'name' => 'QRCode在线解析',
            'url' => 'https://qrcode-api.herokuapp.com/decode',
            'method' => 'POST',
            'data_key' => 'image_url',
            'parser' => function($response) {
                $result = json_decode($response, true);
                if (isset($result['data']) && !empty($result['data'])) {
                    return $result['data'];
                }
                return false;
            }
        ],
        
        // API 3: 简单的GET请求API
        [
            'name' => 'QR解析服务',
            'url' => 'https://api.qrcode.show/v1/decode?url={url}',
            'method' => 'GET',
            'parser' => function($response) {
                $result = json_decode($response, true);
                if (isset($result['success']) && $result['success'] && isset($result['data'])) {
                    return $result['data'];
                }
                return false;
            }
        ],
        
        // API 4: 备用API
        [
            'name' => 'TopScan二维码解析',
            'url' => 'https://qr.topscan.com/api.php?url={url}',
            'method' => 'GET',
            'parser' => function($response) {
                // 简单的文本返回
                if (!empty($response) && strpos($response, 'error') === false && strpos($response, 'failed') === false) {
                    return trim($response);
                }
                return false;
            }
        ],
        
        // API 5: 国内API (需要申请key，但提供免费额度)
        [
            'name' => '聚合数据API',
            'url' => 'http://op.juhe.cn/qrcode/decode',
            'method' => 'GET',
            'params' => [
                'key' => 'your_juhe_key_here', // 需要到聚合数据申请
                'url' => '{url}'
            ],
            'parser' => function($response) {
                $result = json_decode($response, true);
                if (isset($result['error_code']) && $result['error_code'] == 0 && isset($result['result']['content'])) {
                    return $result['result']['content'];
                }
                return false;
            }
        ],
        
        // API 6: 极速API (需要申请key)
        [
            'name' => '极速API',
            'url' => 'https://api.jisuapi.com/qrcode/decode',
            'method' => 'GET',
            'params' => [
                'appkey' => 'your_jisu_key_here', // 需要到极速API申请
                'url' => '{url}'
            ],
            'parser' => function($response) {
                $result = json_decode($response, true);
                if (isset($result['status']) && $result['status'] == 0 && isset($result['result']['content'])) {
                    return $result['result']['content'];
                }
                return false;
            }
        ]
    ],
    
    // API配置
    'config' => [
        'timeout' => 10, // 请求超时时间（秒）
        'retry_count' => 3, // 重试次数
        'retry_delay' => 1000, // 重试延迟（毫秒）
        'log_enabled' => true, // 是否启用日志
        'cache_enabled' => false, // 是否启用缓存
        'cache_ttl' => 3600 // 缓存时间（秒）
    ],
    
    // 错误信息
    'error_messages' => [
        'no_apis' => '没有可用的二维码解析API',
        'all_failed' => '所有二维码解析API都失败了',
        'invalid_url' => '无效的图片URL',
        'network_error' => '网络请求失败',
        'parse_error' => '解析响应失败'
    ]
];
