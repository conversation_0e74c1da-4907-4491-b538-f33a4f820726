<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[type_time]', $typeTimeList, $row['type_time'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type_total')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[type_total]', $typeTotalList, $row['type_total'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select  id="c-name" data-rule="required" class="form-control selectpicker" name="row[name]" data-live-search="true" data-show-subtext="true">
                {foreach name="tableList" item="vo"}
                    <option data-subtext="{$vo.TABLE_COMMENT}" value="{$vo.TABLE_NAME}" {in name="vo.TABLE_NAME" value="$row.name"}selected{/in}>{$vo.TABLE_NAME}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Field_total')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-field_total" style="height:30px;" name="row[field_total]" class="form-control selectpicker" data-value="{$row.field_total|htmlentities}" data-live-search="true" data-show-subtext="true"></select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Field_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-field_time" style="height:30px;" name="row[field_time]" class="form-control selectpicker" data-value="{$row.field_time|htmlentities}" data-live-search="true" data-show-subtext="true"></select>
            <input type="hidden" name="row[field_time_type]" value="{$row.field_time_type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Where')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-where" class="form-control" name="row[where]" placeholder="如：status='normal' 或 id>10" type="text" value="{$row.where|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="icon" class="control-label col-xs-12 col-sm-2">{:__('Icon')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group input-groupp-md">
                <input type="text" class="form-control" id="icon" name="row[icon]" value="{$row.icon}" />
                <a href="javascript:;" class="btn-search-icon input-group-addon">{:__('Search icon')}</a>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="icon" class="control-label col-xs-12 col-sm-2">{:__('Icon_color')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group input-groupp-md">
                <input id="c-icon_color" data-rule="required" class="form-control" name="row[icon_color]" type="text" value="{$row.icon_color|htmlentities}" style="width: 70%">
                <span class="input-group-btn" style="display: unset;"></span>
                <button type="button" class="btn btn-default btn-color colorpicker" style="padding:0;margin-left:1px;" title="选择颜色"><img src="__CDN__/assets/addons/customcharts/img/colorful.png" height="29" alt=""></button>
                <span class="msg-box n-right" for="c-icon_color"></span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_money]', ['1'=>__('Yes'), '0'=>__('No')], $row['is_money'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" data-rule="required" class="form-control" name="row[weigh]" type="number" value="{$row.weigh|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
{include file="auth/rule/tpl" /}