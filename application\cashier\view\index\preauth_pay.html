<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>预授权支付</title>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src= "https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.inc.min.js"></script> 
</head>
<body>
  
  <button id="J_btn" class="btn btn-default">支付</button>
  <script>
    var btn = document.querySelector('#J_btn');
    btn.addEventListener('click', function(){
      ap.tradePay({
        orderStr : '{$data.url}'
      }, function( res ) {
        
        if(res.resultCode == '9000'){

            var alipay_fund_auth_order_app_freeze_response = JSON.parse(res.result).alipay_fund_auth_order_app_freeze_response;
            var data = JSON.stringify(alipay_fund_auth_order_app_freeze_response);
            // 使用post请求接口，不用json
            $.ajax({
                url: 'http://' + window.location.host + '/cashier/index/preauth_pay?data='+data,
                type: 'POST',
                success: function(ress){
                    if(ress.code == 1){
                        // 支付完成
                    }
                },
                error: function(xhr, status, error) {
                    ap.alert('请求失败，请重试');
                }
            });
        } else {
            ap.alert(res.resultCode);
        }
        
        
      });
    });
  </script>
</body>
</html>