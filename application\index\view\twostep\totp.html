<style>
    .panel-recharge h3 {
        margin-bottom: 15px;
        margin-top: 10px;
        color: #444;
        font-size: 16px;
    }

    .row-recharge > div {
        margin-bottom: 10px;
    }

    .row-recharge > div > label {
        width: 100%;
        height: 40px;
        display: block;
        font-size: 14px;
        line-height: 40px;
        color: #999;
        background: #fff;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        cursor: pointer;
        text-align: center;
        border: 1px solid #ddd;
        margin-bottom: 20px;
        font-weight: 400;
    }

    .row-recharge > div > label.active {
        border-color: #0d95e8;
        color: #0d95e8;
    }

    .row-recharge > div > label:hover {
        z-index: 4;
        border-color: #27b0d6;
        color: #27b0d6;
    }

    .panel-recharge .custommoney {
        border: none;
        height: 100%;
        width: 100%;
        display: inherit;
        line-height: 100%;
    }

    .row-recharge > div {
        height: 40px;
        line-height: 40px;
    }

    .row-recharge > div input.form-control {
        border: none;
    }

    .row-paytype div input {
        display: none;
    }

    .row-paytype img {
        height: 22px;
        margin: 8px;
        vertical-align: inherit;
    }

    .btn-recharge {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        padding: 0;
    }

</style>

<div id="content-container" class="container">
    <div class="row">
        {if $twostep_config.center == 1}
        <div class="col-md-3">
            {include file="common/sidenav" /}
        </div>
        {/if}
        <div class="col-md-{if $twostep_config.center == 1}9{else if $twostep_config.center == 0}12{/if}">
            <div class="panel panel-default panel-recharge">
                <div class="panel-body">
                    <h2 class="page-header">{:__('TwoStep')} <span>-{:__('TOTP')}</span></h2>
                    <div class="alert alert-info-light">
                        {:__('Scan Soft')}
                    </div>
                    <div class="clearfix"></div>
                    <form action="{:url('twostep/totpreg')}" method="post">
                        {:token()}
                        <h3>{:__('Scan Qrcode')}</h3>
                        <img src="{$url}">
                        <div class="row row-recharge row-money">
                        </div>
                        <h3>{:__('Secret')} : {$secret}</h3>
                        <h3>{:__('Please input Code')}</h3>
                        <div class="row row-recharge row-money">
                            <div class="col-xs-6 col-sm-4 col-md-3 col-lg-2" id="col-custom">
                                <label>
                                    <input type="number" name="twostep_code" class="form-control" value="" id="totp">
                                </label>
                            </div>
                        </div>

                        <div class="row row-recharge" style="margin:20px -15px 0 -15px;">
                            <div class="col-xs-6 col-sm-4 col-md-4 col-lg-2">
                                <input type="submit" class="btn btn-success btn-recharge btn-block" value="{:__('Verify now')}"/>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var otxt = document.getElementById("totp");
    otxt.focus();
</script>