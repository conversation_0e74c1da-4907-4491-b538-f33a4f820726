define(['jquery', 'bootstrap', 'backend', 'table', 'upload', 'form'], function ($, undefined, Backend, Table,Upload, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'qrcode/index' + location.search,
                    add_url: 'qrcode/add',
                    edit_url: 'qrcode/edit',
                    del_url: 'qrcode/del',
                    multi_url: 'qrcode/multi',
                    import_url: 'qrcode/import',
                    table: 'qrcode',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'url', title: __('Url'), operate: 'LIKE', formatter: Table.api.formatter.url},
                        {field: 'price', title: __('Price'), operate:'BETWEEN'},
                        {field: 'note', title: __('Note'), operate: 'LIKE'},
                        {field: 'account.name', title: __('支付宝账号'), operate: 'LIKE'},
                        {field: 'pulltime', title: __('拉单时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

        },
        add: function () {
            Upload.api.plupload($("#faupload-avatar"), function(data, ret){
                $.ajax("qrcode/deqrcode",{
                   type: "GET",
                   data: {"url":data.fullurl},
                   success: function(data){
                       if(data.code==1){
                           $('#c-url').val(data.qrurl);
                       }
                   },
                   error: function(data){
                        Toastr.error("解析二维码失败");
                    }
                });

            }, function(data, ret){
                console.log(222)
            });
            Controller.api.bindevent();

        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
