<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Response;
use think\Db;
use think\Exception;
/**
 * 示例接口
 * @ApiInternal
 */
class Demo extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = ['qrCode','test','ggg'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['qrCode','test','ggg'];

    public function qrCode($codeText){
        //生成二维码 传生成二维码的文本内容
        $qrCode    = $this->build($codeText);
        header('Content-Type: ' . $qrCode->getContentType());
        $mimetype = 'png';
        $response = Response::create()->header("Content-Type", $mimetype);
        echo $qrCode->writeString();die;
        $response->content($qrCode->writeString());
//        $qrCodeUrl =  $this->serverUrl().'/uploads/qrcode/'.$qrCode;
//        $this->success('返回成功', ['qrCodeUrl' => $qrCodeUrl]);
    }

    public function serverUrl(){
        $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        return  $http_type . $_SERVER['HTTP_HOST'];
    }

    // 生成二维码
    public function build($text)
    {
        $config = get_addon_config('qrcode');
        $params = $this->request->get();
        $params = array_intersect_key($params, array_flip(['text', 'size', 'padding', 'errorlevel', 'foreground', 'background', 'logo', 'logosize', 'logopath', 'label', 'labelfontsize', 'labelalignment']));

        $params['text'] = $text;
        $params['label'] = '';
        $qrCode = \addons\qrcode\library\Service::qrcode($params);
//        $mimetype = $config['format'] == 'png' ? 'image/png' : 'image/svg+xml';

//        $response = Response::create()->header("Content-Type", $mimetype);

        // 直接显示二维码
//         header('Content-Type: ' . $qrCode->getContentType());

//         $response->content($qrCode->writeString());

        // 写入到文件
//        if ($config['writefile']) {
//            $qrcodePath = ROOT_PATH . 'public/uploads/qrcode/';
//            if (!is_dir($qrcodePath)) {
//                @mkdir($qrcodePath);
//            }
//            if (is_really_writable($qrcodePath)) {
//                $filePath = $qrcodePath . md5(implode('', $params)) . '.' . $config['format'];
//                $qrCode->writeFile($filePath);
//                $code_name = md5(implode('', $params)) . '.' . $config['format'];
//            }
//        }

//        return $code_name;
        return $qrCode;
    }
    
    public function test()
    {
        // 使用正则表达式匹配URL
        $pattern = '/https?:\/\/[^\s]+/';
        // 检查字符串中是否有匹配正则表达式的URL
        if (preg_match($pattern, "https:///api/Client/notify", $matches)) {
            // 如果有匹配，验证URL的有效性
             dump(filter_var($matches[0], FILTER_VALIDATE_URL) !== false);die;
        }
        return "false";
    }
    
    public function ggg()
    {
        // trace("afaaa","error");
        \think\Log::write('测试日志信息1111');
    }
}
