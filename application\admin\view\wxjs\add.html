<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('商户名称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('商户号')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account_identity" data-rule="required" class="form-control" name="row[account_identity]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('公众号')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-gzh_name" data-rule="required" class="form-control" name="row[gzh_name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('appid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-appid" data-rule="required" class="form-control" name="row[appid]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('app_key')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-app_key" data-rule="required" class="form-control" name="row[app_key]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('api_key')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-api_key" data-rule="required" class="form-control" name="row[api_key]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('短剧站域名')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-game_url" data-rule="required" class="form-control" name="row[game_url]" type="text" value="">
        </div>
    </div>
    

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('最小金额')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-min_price" data-rule="required" class="form-control" name="row[min_price]" type="number" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('最大金额')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-max_price" data-rule="required" class="form-control" name="row[max_price]" type="number" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('并发限制(每分钟)')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-maximum" data-rule="required" class="form-control" name="row[maximum]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('收款笔数限制')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_limit" data-rule="required" class="form-control" name="row[pay_limit]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('通道编码')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-channel" data-rule="required" class="form-control" name="row[channel]" type="text" value="8001" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="normal"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('备注')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-notes" data-rule="required" class="form-control" name="row[notes]" type="text" value="">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
