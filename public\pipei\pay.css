body {
	background:#f2f2f4;
}
body,html {
	width:100%;
	height:100%;
}
*,:after,:before {
	box-sizing:border-box;
}
* {
	margin:0;
	padding:0;
}
img {
	max-width:100%;
}

.btn {
    display: inline-block;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    text-decoration: none
}

.btn:focus,.btn:active:focus,.btn.active:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

.btn:hover,.btn:focus {
    color: #ffffff;
    text-decoration: none
}

.btn:active,.btn.active {
    outline: 0;
    background-image: none;
    -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,0.125);
    box-shadow: inset 0 3px 5px rgba(0,0,0,0.125)
}



.btn-success {
    color: #fff;
    background-color: #5cb85c;
    border-color: #4cae4c
}



#header {
	height:60px;
	border-bottom:2px solid #eee;
	background-color:#fff;
	text-align:center;
	line-height:60px;
}
#header h1 {
	font-size:20px;
}
#main {
	overflow:hidden;
	margin:0 auto;
	padding:20px;
	padding-top:80px;
	width:992px;
	max-width:100%;
}
#main .left {
	float:left;
	width:40%;
}
.left p {
	margin:10px auto;
}
.make {
	padding-top:15px;
	border-radius:10px;
	background-color:#fff;
	box-shadow:0 3px 3px 0 rgba(0,0,0,.05);
	color:#666;
	text-align:center;
	transition:all .2s linear;
}
.make .qrcode {
	margin:auto;
}
.make .money {
	margin-bottom:0;
	color:#f44336;
	font-weight:600;
	font-size:18px;
}
.info {
	padding:15px;
	width:100%;
	border-radius:0 0 10px 10px;
	background:#32343d;
	color:#f2f2f2;
	text-align:center;
	font-size:14px;
}
#main .right {
	float:right;
	padding-top:25px;
	width:60%;
	color:#ccc;
	text-align:center;
}
@media (max-width:768px) {
	#main {
	padding-top:30px;
}
#main .left {
	width:100%;
}
#main .right {
	display:none;
}
}