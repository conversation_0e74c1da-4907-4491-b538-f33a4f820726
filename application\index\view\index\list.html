<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0 user-scalable=no">
  <meta name="format-detection" content="telephone=no">
  <title>直营商城</title>
  <link rel="stylesheet" type="text/css" href="__CDN__/shop/Mao2.min.css">
  <link rel="stylesheet" type="text/css" href="__CDN__/shop/style2.css">
  <link rel="stylesheet" type="text/css" href="__CDN__/shop/Mao2.diy.css">
  <link rel="stylesheet" type="text/css" href="__CDN__/shop/iconfont.css">
  <script src="__CDN__/shop/jquery-2.1.1.min.js"></script>
  <script src="__CDN__/shop/layer.js"></script>
  <script src="__CDN__/shop/Mao.js"></script>
</head>
<body>
<div class="fui-page-group">
  <div class="fui-page  fui-page-current " style="top: 0; background-color: #fafafa;">
    <div class="fui-header jb">
      <div class="fui-header-left">
        <a onclick="goBack()" class="back" style="color: #f7f7f7;"></a>
      </div>
      <div class="title">全部商品</div>
    </div>
    <div class="fui-content navbar" style="background-color: #f3f3f3; padding-bottom: 0;">
      <div class="fui-notice" style="background: #ffffff; border-color: ; margin-bottom: 0px;" data-speed="4">
        <div class="icon">
          <i class="icon icon-notification1" style="font-size: 0.7rem; color: #fd5454;"></i>
        </div>
        <div class="text" style="color: #666666;">
          <ul>
            <li>
              <a href="javascript:;" style="color: #666666;" data-nocache="true">
                <marquee behavior="scroll" scrolldelay="100" scrollamount="5">
                  同款搭建联系乐鱼科技：BW5653                                </marquee>
              </a>
            </li>
          </ul>
        </div>
      </div>
      <div class="fui-cell-group">
        <div id="lazyComponent12" class="lazy-component lazy-component__image" style="margin:0px;padding:0px;border:0px;font-family:-apple-system, BlinkMacSystemFont, &quot;font-size:16px;vertical-align:initial;color:#333333;background-color:#CEE6FD;">
          <div class="cap-title cap-title--normal" style="margin:0px;padding:10px;border:0px;font-style:inherit;font-weight:inherit;font-family:inherit;vertical-align:initial;background-color:#F8F8F8;">
            <h2 class="cap-title__main" style="font-style:inherit;font-weight:inherit;font-family:inherit;font-size:18px;vertical-align:initial;">
              商品列表
            </h2> </div></div>
        <div class="fui-goods-group block three" style="background: ;" id="shop_list">                <a class="fui-goods-item" style="position: relative;width:100%" href="{:url('/index/index/goods',['id' => 1])}">
          <div class="imagezdy triangle" data-text="推荐" data-lazyloaded="true" style="background-image: url('https://cbu01.alicdn.com/img/ibank/O1CN01G7jrWK1rZutB5kl8V_!!*************-0-cib.jpg');"></div>
          <div class="detail">
            <div class="nametj" style="color: #262626;">
              【人气销量】 港式金汤鲍鱼花胶鸡加热即食鱼胶滋补食材1650g礼盒                        </div>
            <div class="price">
                            <span class="text" style="color: #ed2822;">
                                <p class="minprice">¥ 0.01</p>
                            </span>
              <span class="buy buybtn-3" style="background-color: #01a1ff;margin-right: 5px;">邮</span>                            <span class="buy buybtn-3" style="background-color: #fe5455;"><i class="icon icon-cartfill"></i></span>
            </div>
          </div>
        </a>
          <a class="fui-goods-item" style="position: relative;width:100%" href="{:url('/index/index/goods',['id' => 1])}">
            <div class="imagezdy triangle" data-text="推荐" data-lazyloaded="true" style="background-image: url('https://cbu01.alicdn.com/img/ibank/2020/248/667/21384766842_493672081.jpg');"></div>
            <div class="detail">
              <div class="nametj" style="color: #262626;">
                【天猫优选】 土耳其海参干货批发 野生希腊黑海参土耳其淡干海参                        </div>
              <div class="price">
                            <span class="text" style="color: #ed2822;">
                                <p class="minprice">¥ 0.01</p>
                            </span>
                <span class="buy buybtn-3" style="background-color: #01a1ff;margin-right: 5px;">邮</span>                            <span class="buy buybtn-3" style="background-color: #fe5455;"><i class="icon icon-cartfill"></i></span>
              </div>
            </div>
          </a>
          <a class="fui-goods-item" style="position: relative;width:100%" href="{:url('/index/index/goods',['id' => 1])}">
            <div class="imagezdy triangle" data-text="推荐" data-lazyloaded="true" style="background-image: url('https://cbu01.alicdn.com/img/ibank/O1CN01dx51001rZutH59CkV_!!*************-0-cib.jpg');"></div>
            <div class="detail">
              <div class="nametj" style="color: #262626;">
                【天猫优选】 佛跳墙海鲜鲍鱼花胶海参干贝加热即食大盆菜1.5KG节日礼盒装送礼                        </div>
              <div class="price">
                            <span class="text" style="color: #ed2822;">
                                <p class="minprice">¥ 188.00</p>
                            </span>
                <span class="buy buybtn-3" style="background-color: #01a1ff;margin-right: 5px;">邮</span>                            <span class="buy buybtn-3" style="background-color: #fe5455;"><i class="icon icon-cartfill"></i></span>
              </div>
            </div>
          </a>
          <a class="fui-goods-item" style="position: relative;width:100%" href="{:url('/index/index/goods',['id' => 1])}">
            <div class="imagezdy triangle" data-text="推荐" data-lazyloaded="true" style="background-image: url('https://cbu01.alicdn.com/img/ibank/O1CN01dx51001rZutH59CkV_!!*************-0-cib.jpg');"></div>
            <div class="detail">
              <div class="nametj" style="color: #262626;">
                【天猫优选】 佛跳墙海鲜鲍鱼花胶海参干贝加热即食大盆菜1.5KG节日礼盒装送礼                        </div>
              <div class="price">
                            <span class="text" style="color: #ed2822;">
                                <p class="minprice">¥ 1888.00</p>
                            </span>
                <span class="buy buybtn-3" style="background-color: #01a1ff;margin-right: 5px;">邮</span>                            <span class="buy buybtn-3" style="background-color: #fe5455;"><i class="icon icon-cartfill"></i></span>
              </div>
            </div>
          </a>
          <script>
            $('#page').html('');
          </script>
          <script>
            $('#ts').text('共：4件商品');
          </script>
        </div>
        <div id="page"></div>

      </div>

    </div>
  </div>
  <div class="fui-navbar">
    <a href="{:url('/')}" class="external nav-item">
      <span class="icon icon-home"></span>
      <span class="label">首页</span>
    </a>
    <a href="{:url('/index/index/list')}" class="external nav-item active">
      <span class="icon icon-list"></span>
      <span class="label">全部商品</span>
    </a>
    <a href="{:url('/index/index/ordersearch')}" class="external nav-item ">
      <span class="icon icon-daifukuan1"></span>
      <span class="label">订单查询</span>
    </a>
  </div>
</div>
<script>
  var loading = '<div class="infinite-loading"><span class="fui-preloader"></span><span class="text"> 正在加载...</span></div>';
  $(function() {
    // list(0);
  });
  function list(id) {
    $("#shop_list").html(loading);
    Mao.postData('../api/data.php?mod=list&lx=1&type='+id, '', function(d) {
      $("#shop_list").html(d);
      return false
    });
  }
  function page(lx,id,page,search) {
    var loading = layer.load();
    Mao.postData('../api/data.php?mod=list&lx='+lx+'&type='+id+'&page='+page+'&search='+search, '', function(d) {
      layer.close(loading);
      $("#shop_list").append(d);
      return false
    });
  }
  function search() {
    layer.prompt({title: '搜索商品', formType: 0}, function(name, index){
      var loading = layer.load();
      Mao.postData('../api/data.php?mod=list&lx=2&search='+name+'', '', function(d) {
        layer["closeAll"]();
        $("#shop_list").html(d);
        return false
      });
    });
  }
</script>
</body>
</html>