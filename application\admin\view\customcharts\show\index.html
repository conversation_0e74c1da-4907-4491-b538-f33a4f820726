<style>
    .panel-statistics h4 {
        color: #666;
        font-weight: 400;
        font-size: 14px;
    }
    .panel-statistics h3 {
        font-weight: 500;
        font-size: 18px;
        color: #333;
    }
    .panel-statistics em {
        font-style: normal;
    }
    .panel-statistics .pull-right {
        padding-right: 10px;
    }
    .panel-statistics .table thead tr th {
        font-weight: normal;
    }
    .panel-statistics .table tbody tr td {
        font-weight: normal;
        vertical-align: middle;
    }
    .panel-statistics .table tbody tr td p {
        margin: 0;
    }
    #echarts1 textarea {
        display: block;
    }
    .table {
        margin-bottom: 0px;
    }
</style>
<div class="btn-refresh hidden" id="resetecharts"></div>
<div class="row">
    {foreach name="totalNumber" item="vo"}
    <div class="col-xs-6 col-sm-3" style="margin-bottom: 20px;">
        <div class="panel panel-default panel-intro panel-statistics">
            <div class="panel-body">
                <div class="pull-left">
                    <h4>{$vo.title}<span style="font-size: 12px;color: #AFAFAF;">（{$vo.subtitle}）</span></h4>
                    <h3>
                        {if condition="$vo.is_money"}
                        ￥{$vo.total|sprintf='%.2f',###}
                        {else}
                        {$vo.total}
                        {/if}
                        {if condition="$vo.ratio != 0"}
                        {if condition="$vo.is_money"}
                        <em data-toggle="tooltip" data-title="上期：￥{$vo.lastTotal|sprintf='%.2f',###}" class="text-{:$vo.ratio>=0?'success':'danger'}">{:$vo.ratio>=0?'+':''}{$vo.ratio}%</em>
                        {else}
                        <em data-toggle="tooltip" data-title="上期：{$vo.lastTotal}" class="text-{:$vo.ratio>=0?'success':'danger'}">{:$vo.ratio>=0?'+':''}{$vo.ratio}%</em>
                        {/if}
                        {/if}
                    </h3>
                </div>
                <div class="pull-right" style="color:{$vo.icon_color|default='#333333'};">
                    <i class="{$vo.icon} fa-4x"></i>
                </div>
            </div>
        </div>
    </div>
    {/foreach}
</div>


<div class="row" style="margin-bottom: 20px;">
    <div class="col-xs-12">
        <div class="panel panel-default panel-intro panel-statistics">
            <div class="panel-body">
                <div id="datefilter">
                    <form id="form1" action="" role="form" novalidate class="form-inline">
                        <a href="javascript:;" class="btn btn-primary btn-refresh"><i class="fa fa-refresh"></i></a>
                        <a href="javascript:;" class="btn btn-success btn-filter">{:__('Today')}</a>
                        <a href="javascript:;" class="btn btn-success btn-filter">{:__('Yesterday')}</a>
                        <a href="javascript:;" class="btn btn-success btn-filter">{:__('Last 7 Days')}</a>
                        <a href="javascript:;" class="btn btn-success btn-filter">{:__('Last 30 Days')}</a>
                        <a href="javascript:;" class="btn btn-success btn-filter">{:__('Last month')}</a>
                        <a href="javascript:;" class="btn btn-success btn-filter">{:__('This month')}</a>
                        <a href="javascript:;" class="btn btn-success btn-filter">今年</a>
                        <a href="javascript:;" class="btn btn-success btn-filter">去年</a>
                        <div class="input-group">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                            <input type="text" class="form-control input-inline datetimerange" id="customcharts_datetimerange" data-time-picker="false" autocomplete="off" placeholder="指定日期" style="width:280px;"/>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="row" style="margin-bottom: 5px;">
    {foreach name="totalChart" item="vo"}
    <div class="col-xs-12 col-sm-6">
        <div class="panel">
            <div class="panel-body">
                <div id="echarts{$vo.id}" style="height:360px;"></div>
                <a href="javascript:" class="btn btn-refresh hidden">{$vo.title}</a>
            </div>
        </div>
    </div>
    {/foreach}
</div>


<div class="row">
    {foreach name="totalRanking" item="vo"}
    <div class="col-xs-12 col-sm-4" style="margin-bottom: 20px;">
        <div class="panel panel-default panel-intro panel-statistics">
            <div class="panel-body">
                <table class="table" style="width:100%">
                    <thead>
                    <tr>
                        <th width="12%">排名</th>
                        <th width="40%">{$vo.title}</th>
                        <th width="25%" class="text-center">{$vo.unit}</th>
                        <th class="text-center">占比</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach name="vo.data" id="item" empty="
                    <tr>
                        <td colspan='4' class='text-center'>暂无数据</td>
                    </tr>
                    "}
                    <tr>
                        <td>
                            <p>{++$key}</p>
                        </td>
                        <td>
                            <p style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 200px;" title="{$item.name}">{$item.name}</p>
                        </td>
                        <td>
                            <h5 class="text-center">{$item.nums}</h5>
                        </td>
                        <td>
                            <div class="progress">
                                <div class="progress-bar progress-bar-success" data-toggle="tooltip" data-title="{$item['ratio']}%" style="color: #333;text-align:left;width: {$item['ratio']}%">&nbsp;{$item['ratio']}%</div>
                            </div>
                        </td>
                    </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {/foreach}
</div>

{if !$totalNumber && !$totalChart && !$totalRanking}
<div class="row">
    <div class="col-xs-12">
        <div class="alert alert-danger-light">
            <i class="fa fa-exclamation-triangle"></i>
            <b>提示：</b>您还没有添加任何数据统计项，请添加后查看统计结果。
        </div>
    </div>
</div>
{/if}