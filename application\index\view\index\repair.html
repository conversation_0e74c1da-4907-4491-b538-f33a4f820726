<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0 user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <title>补齐信息</title>
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/Mao1.min.css">
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/style.css">
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/Mao.diy.css">
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/iconfont.css">
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/dz.css">
    <link rel="stylesheet" href="__CDN__/shop/layui/css/layui.css">
    <script src="__CDN__/shop/jquery-2.1.1.min.js"></script>
    <script src="__CDN__/shop/layer.js"></script>
    <script src="__CDN__/shop/Mao.js"></script>
</head>
<body>
<div class="fui-page-group statusbar">
    <div class="fui-page fui-page-current order-pay-page">
        <div class="fui-header jb">
            <div class="fui-header-left">
                <a onclick="goBack()" class="back" style="color: #f7f7f7;"></a>
            </div>
            <div class="title">补齐订单信息</div>
            <div class="fui-header-right">
            </div>
        </div>
        <div class="fui-content navbar" style="bottom: 0rem;padding-bottom: 0rem;">
            <div class="fui-cell-group">
                <div class="fui-cell">
                    <div class="fui-cell-label">收件人姓名</div>
                    <div class="fui-cell-info c000">
                        <input type="text" class="fui-input" id="xm" placeholder="请输入收件人姓名">
                    </div>
                </div>
                <div class="fui-cell">
                    <div class="fui-cell-label">收件人手机</div>
                    <div class="fui-cell-info c000">
                        <input type="text" class="fui-input" id="shouji" placeholder="请输入收件人手机号">
                    </div>
                </div>
                <div class="fui-cell">
                    <div class="fui-cell-label">收件人地址</div>
                    <div class="fui-cell-info c000">
                        <input type="text" class="fui-input" id="dz" placeholder="请选择收件地址">
                    </div>
                </div>
                <div class="fui-cell">
                    <div class="fui-cell-label">详细地址</div>
                    <div class="fui-cell-info c000">
                        <input type="text" class="fui-input" id="xxdz" placeholder="请输入详细街道地址">
                    </div>
                </div>
                <div class="fui-cell">
                    <div class="fui-cell-label">圈口</div>
                    <div class="fui-cell-info c000">
                        <input type="text" class="fui-input" id="ly" placeholder="可以为空">
                    </div>
                </div>
            </div>
            <div class="fui-cell-group fui-cell-click transparent">
                <a class="fui-cell external btn-mao" onclick="submit()">
                    <div class="fui-cell-text jb" style="text-align: center;">
                        <p>保 存</p>
                    </div>
                </a>
            </div><br>
        </div>
    </div>
</div>
<script src="__CDN__/shop/city.js"></script>
<script src="__CDN__/shop/address.js"></script>
<script src="__CDN__/shop/layui/layui.js"></script>
<script>
    layui.use('upload', function(){
        var upload = layui.upload;
        upload.render({
            elem: '#test1'
            ,url: '../api/api.php?mod=upload&type=1'
            ,done: function(res){
                layer.msg(res.msg, function(){
                    if(res.code == 0){
                        $('#mgz').val(res.name)
                    }
                });
            }
            ,error: function(){
                layer.msg('~连接服务器失败！', {icon: 5});
            }
        });
        upload.render({
            elem: '#test2'
            ,url: '../api/api.php?mod=upload&type=1'
            ,done: function(res){
                layer.msg(res.msg, function(){
                    if(res.code == 0){
                        $('#sfz1').val(res.name)
                    }
                });
            }
            ,error: function(){
                layer.msg('~连接服务器失败！', {icon: 5});
            }
        });
        upload.render({
            elem: '#test3'
            ,url: '../api/api.php?mod=upload&type=1'
            ,done: function(res){
                layer.msg(res.msg, function(){
                    if(res.code == 0){
                        $('#sfz2').val(res.name)
                    }
                });
            }
            ,error: function(){
                layer.msg('~连接服务器失败！', {icon: 5});
            }
        });
    });
    !function() {
        var $target = $('#dz');
        $target.citySelect();
        $target.on('click', function(event) {
            event.stopPropagation();
            $target.citySelect('open');
        });
        $target.on('done.ydui.cityselect', function(ret) {
            $(this).val(ret.provance + ' ' + ret.city + ' ' + ret.area);
        });
    }();
    function submit() {
        var loading = layer.load();
        $.ajax({
            url: '../api/api.php',
            type: 'POST',
            dataType: 'json',
            data: {
                mod: "repair",
                ddh: "20241111085201215",
                xm: $("#xm").val(),
                dz: $("#dz").val(),
                xxdz: $("#xxdz").val(),
                ly: $("#ly").val(),
                jzxm: $("#jzxm").val(),
                sfzh: $("#sfzh").val(),
                mgz: $("#mgz").val(),
                sfz1: $("#sfz1").val(),
                sfz2: $("#sfz2").val(),
                shouji: $("#shouji").val()
            },
            success: function (a) {
                layer.close(loading);
                if (a.code == 0) {
                    window.location.href="/orderpay.php";
                } else {
                    layer.msg(a.msg);
                }
            },
            error: function() {
                layer.close(loading);
                layer.msg('~连接服务器失败！', {icon: 5});
            }
        });
    }
</script>
</body>
</html>
