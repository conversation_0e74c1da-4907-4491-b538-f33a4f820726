<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 首页接口
 * @ApiInternal
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 首页
     *
     */
    public function index()
    {
        $this->success('请求成功');
    }

    public function getaccount()
    {
        $appid = $this->request->param('appid');
        $account = db('account')->where('appid',$appid)->find();
        return json(['code' => 1,'msg' => '请求成功', 'data' => $account]);
    }

    public function getorder()
    {
        $orderid = $this->request->param('orderid');
        $order = db('order')->where('mc_order',$orderid)->find();
        return json(['code' => 1,'msg' => '请求成功', 'data' => $order]);
    }

}
