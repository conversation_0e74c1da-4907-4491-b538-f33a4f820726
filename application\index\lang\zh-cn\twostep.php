<?php
return [
    'TwoStep'        => '两步验证',
    'Safe Center'        => '安全中心',
    'TOTP'        => '动态口令',
    'WebAuthn'        => '安全密钥',
    'Not Set twostep'        => '未设置两步验证',
    'Not Set TOTP'        => '未设置 动态口令',
    'Not Set WebAuthn'        => '未设置 安全密钥',
    'Choose TwoStep'        => '请选择两步验证类型',
    'Next'        => '下一步',
    'Verify now'        => '验证',
    'Secret'  => '动态密钥',
    'Code'  => '验证码',
    'Scan Qrcode'     => '请扫描下方二维码',
    'Scan Soft'        => '请在应用商店内搜索 Authy (推荐,手机号绑定)或者 Microsoft Authenticator 扫描下方二维码',
    'Please input Code'        => '请输入验证器生成的验证码',
    'Secret_error'        => '动态密钥错误',
    'Checkcode_error'        => '动态口令验证错误',
    'You are not logged in'                 => '你当前还未登录',
    'Has been Set twostep ok'                 => '您已经配置过两步验证了',
    'Has been Set TOTP'                 => '您已经配置过动态口令了',
    'Has been Set WebAuthn'                 => '您已经配置过安全密钥了',
    'TOTP set ok'                 => '动态口令设置成功',
    'WebAuthn set ok'                 => '安全密钥设置成功',
    'Cancel TwoStep'                 => '取消两步验证',
    'Cancel TOTP'                 => '取消 动态口令',
    'Cancel WebAuthn'                 => '取消 安全密钥',
    'Cancel TOTP ok'                 => '取消 动态口令 成功',
    'Cancel WebAuthn ok'                 => '取消 安全密钥 成功',
    'None'                 => '无数据',
];