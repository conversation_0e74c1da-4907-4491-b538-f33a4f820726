<?php

namespace app\admin\controller\order;

use app\common\controller\Backend;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\Log;
use fast\Http;

/**
 * 支付宝预授权订单
 *
 * @icon fa fa-circle-o
 */
class Alipaypreauth extends Backend
{

    /**
     * Order模型对象
     * @var \app\admin\model\Order
     */
    protected $model = null;
    protected $dataLimit = "auth";

    protected $dataLimitField = "admin_id";

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Order;
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['merchants','admin'])
                    ->where($where)
                    ->where('channel_code','9002')
                    ->order($sort, $order)
                    // ->distinct(true)
                    ->paginate($limit);
            
            // try {
            //     $x = $this->model->getLastSql();
            //     Log::write($x,'notice');
            // } catch (PDOException|Exception $e) {
            //   Log::write($e,'notice');
            // }
            
            foreach ($list as $row) {
				$row->getRelation('merchants')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 订单回调
     */
    public function callback($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $row = $this->model->where($pk,$ids)->find();
        if ($row->pay_url == '') {
            $this->error(__('支付连接未生成不能回调'));
        }

        if ($row->callback_status == 1) {
            $this->error(__('请不要重复回调'));
        }
        // 订单未支付
        if ($row->pay_status == 0) {
            // 此处要加入订单状态查询逻辑

            // 修改订单支付状态
            Db::startTrans();
            try {
                $row->save(['pay_status' => 1]);
                Db::commit();
            } catch (PDOException|Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }

        }

        // 订单已支付,则执行商户回调逻辑
        $merchants = db('merchants')->where('id',$row->merchants_id)->find();
        $rs = \app\common\library\Order::merchantsCallback($row->callback_url, $merchants['key'], $row->out_trade_no, $row->amount, $row->channel_code, 1);
        if (!$rs) {
            $this->error(__('商户回调失败'));
        }
        Db::startTrans();
        try {
            $row->save(['callback_status' => 1, 'callback_time' => time()]);
            Db::commit();
            $this->success(__('回调成功'));
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

}
