define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'tenant/index',
                    add_url: 'tenant/add',
                    edit_url: 'tenant/edit',
                    del_url: 'tenant/del',
                    multi_url: 'tenant/multi',
                }
            });

            var table = $("#table");

            //在表格内容渲染完成后回调的事件
            table.on('post-body.bs.table', function (e, json) {
                $("tbody tr[data-index]", this).each(function () {
                    if (parseInt($("td:eq(1)", this).text()) == Config.admin.id) {
                        $("input[type=checkbox]", this).prop("disabled", true);
                    }
                });
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                columns: [
                    [
                        {field: 'state', checkbox: true, },
                        {field: 'id', title: 'ID'},
                        {field: 'username', title: __('Username')},
                        {field: 'nickname', title: __('Nickname'), operate: 'LIKE'},
                        {field: 'aweme_gold', title: __('抖音额度')},
                        {field: 'today', title: __('今日充值')},
                        {field: 'yesterday', title: __('昨日充值')},
                        {field: 'beforeday', title: __('前日充值')},
                        {field: 'groups_text', title: __('Group'), formatter: Table.api.formatter.search, searchList: {"3":"租户","5":"一级核销","6":"二级核销"}},
                        {field: 'parent.nickname', title: __('Parent'), formatter: Table.api.formatter.search},
                        {field: 'status', title: __("Status"), searchList: {"normal":__('正常'),"hidden":__('禁用')}, formatter: Table.api.formatter.status},
                        {field: 'logintime', title: __('Login time'), formatter: Table.api.formatter.datetime, operate: false, addclass: 'datetimerange', sortable: true},
                        // {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: function (value, row, index) {
                        //         if(row.id == Config.admin.id){
                        //             return '';
                        //         }
                        //         return Table.api.formatter.operate.call(this, value, row, index);
                        //     }}
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            buttons :[
                                {name: 'score', text: '', title: '增减额度', icon: 'fa fa-money', classname: 'btn btn-xs btn-primary btn-dialog', url: 'tenant/score'}
                            ],
                            events: Table.api.events.operate,
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Form.api.bindevent($("form[role=form]"));
        },
        edit: function () {
            Form.api.bindevent($("form[role=form]"));
        },
        score: function () {
            Form.api.bindevent($("form[role=form]"));
        }
    };
    return Controller;
});
