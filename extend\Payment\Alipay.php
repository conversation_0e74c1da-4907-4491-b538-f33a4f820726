<?php
namespace Payment;

use think\Db;
use ChineseUsernameGenerator;
use fast\Http;
use think\Request;

class Alipay
{
    
    public static function QUICKWAPWAY($order)
    {
        $isplay = false;
        Db::startTrans();
        try {
            $order = \app\admin\model\Order::where('id', $order['id'])->lock(true)->find();
            if (!$order['pay_url']) {
                $account = db('account')->where('account_identity',$order['account_identity'])->find();
                $config = json_decode($account['config'],true);

                $djurl = $config['game_url'] . '/index.php';

                do {
                    $username = ChineseUsernameGenerator::generate_username();
                } while (mb_strlen($username, 'UTF-8') < 6);
                

                $res = Http::post($djurl."/user/reg.html?user_name={$username}&user_pwd=123123&user_pwd2=123123");
                trace("注册短剧账号：".$res,"error");
                if ($res == ''){
                    throw new \Exception("注册失败0");
                }
                
                $res = json_decode($res,true);
                if ($res['code']!==1 && $res['code']!==1005){
                    throw new \Exception("注册失败1");
                }

                $res = Http::get($djurl."/user/newbuy.html?price=".$order['amount']."&flag=pay&user=".$username);
                trace("生成短剧订单：".$res,"error");
                if ($res == ''){
                    throw new \Exception("下单失败0");
                }
                
                $res = json_decode($res,true);
                if ($res['code']!==1){
                    throw new \Exception("下单失败1");
                }

                $pay_url = $djurl."/user/newgopay.html?order_code=".$res['data']['order_code']."&payment=Tengpay&user=".$username;
                db('order')->where('id',$order['id'])->update(['pay_url'=>$pay_url,'pay_trade_no'=>$res['data']['order_code']]);
            } else {
                $isplay = true;
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            trace($e->getMessage(), 'error');
            return ['code'=>0,'url'=>'','msg'=>'匹配失败'];
        }
        if ($isplay) {
            return ['code'=>1,'url'=>$order['pay_url'],'msg'=>'匹配成功'];
        } else {
            return ['code'=>1,'url'=>$pay_url,'msg'=>'匹配成功'];
        }
    }
}