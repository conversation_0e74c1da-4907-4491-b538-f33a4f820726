<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use think\Exception;
use think\Request;
use ChineseUsernameGenerator;

class Notify extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    // 支付宝 线上资金冻结授权回调接口
    public function alipay_preauth()
    {
        $params = input();
        trace("冻结回调:".json_encode($params),'error');
        if($params['operation_type']=="FREEZE" && $params["status"]=="SUCCESS"){
            // 冻结成功
            db('order')->where('out_trade_no',$params['out_order_no'])->update(['auth_no'=>$params["auth_no"]]);
            $order = db('order')->where('out_trade_no',$params['out_order_no'])->find();
            if(!$order) return 'success';
            $account = db('account')->where('account_identity',$order['account_identity'])->find();
            $account_config = json_decode($account['config'],true);
            $account_config['private_key'] = config('site.alipay_private_key');
            $account_config['public_key'] = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjpIdrJXm+LH6ZbMstR0gV3MInKrRYeGE0upMCuk0Qzg39SaN4N3ExCCbqGryirRcoNuO9Em/Cm/dvNmeOegb7Dc+Ni/itRuPtOEJwHYAPmZRbAa9dgpMIq+VmxU2HyWzsl+C0KfajCJKOzuHJEAEnuhihArRi1AXXD6Gv3PWHw6LL+odtUuu3GRcVhCfRK8eKTDksi1YGcy4fcv7CS55PhsVCk0GpPDoKjEM/iEftfV/nLnxkkkazfzY/JBOp3E85oycXwwdM4iSzEe2RcIyAmYa0P84pMGYaeurH/DOn6DGKWrHprLPZ6eo4eExBheF6E3ZI3NfShi1kaBn3tvIAQIDAQAB";
            $res = \Payment\Service::froZenTopay($account_config['appid'],$account_config['public_key'],$account_config['private_key'],$params['out_order_no'],$order['amount'],$params['auth_no']);
            if($res['success']){
                db('order')->where('id',$order['id'])->update(['pay_trade_no'=>$res['trade_no']]);
                return 'success';
            }else{
                \Payment\Service::unfreeze($account_config['appid'],$account_config['public_key'],$account_config['private_key'],$order['local_trade_no'],$order['amount'],$order['auth_no']);
                return 'success';
            }
            return 'success';
        }
        return 'success';
    }

    // 支付宝 线上资金冻结转支付回调接口
    public function alipay_trade()
    {
        $params = input();
        trace("扣款回调:".json_encode($params),'error');
        $out_trade_no = $params['out_order_no'];
        $order = \app\admin\model\Order::where('local_trade_no', $out_trade_no)->find();
        if (!$order) {
            return 'fail'; 
        }
        if(isset($params['trade_status']) && $params['trade_status']=="TRADE_SUCCESS"){
            // 扣款成功
            if ($order->pay_status == 0) {
                // 修改支付状态
                $order->pay_status = 1;
                $order->save();
                $merchants = \app\admin\model\Merchants::where('id', $order->merchants_id)->find();

                // 给商户回调
                if (\app\common\library\Order::merchantsCallback($order->callback_url, $merchants->key, $order->out_trade_no, $order->amount, $order->channel_code, $order->pay_status)) {
                    $order->callback_status = 1;
                    $order->callback_time = time();
                    $order->save();
                    return 'success';
                } else {
                    return 'fail';
                }
            }

            if ($order->pay_status == 1 && $order->callback_status == 0) {
                $merchants = \app\admin\model\Merchants::where('id', $order->merchants_id)->find();
                // 给商户回调
                if (\app\common\library\Order::merchantsCallback($order->callback_url, $merchants->key, $order->out_trade_no, $order->amount, $order->channel_code, $order->pay_status)) {
                    $order->callback_status = 1;
                    $order->callback_time = time();
                    $order->save();
                    return 'success';
                } else {
                    return 'fail';
                }
            }

            return 'success';
        }
        if($params['operation_type']=="UNFREEZE" && $params['status']=="SUCCESS"){
            // 押金退还成功
            return 'success';
        }
        return 'fail';
    }
}
