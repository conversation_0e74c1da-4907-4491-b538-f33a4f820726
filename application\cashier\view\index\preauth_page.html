<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全租 - 预授权支付</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.inc.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Helvetica Neue", STHeiti, "Microsoft Yahei", Tahoma, Simsun, sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部品牌栏 -->
    <div class="gradient-bg text-white py-4 px-4 shadow-md">
        <div class="container mx-auto max-w-md flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                <h1 class="text-xl font-bold">安全租</h1>
            </div>
            <span class="text-sm bg-white bg-opacity-20 px-3 py-1 rounded-full">安全支付</span>
        </div>
    </div>

    <div class="container mx-auto px-4 py-6 max-w-md">
        <!-- 订单信息卡片 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-lg font-bold text-gray-800">订单信息</h2>
            </div>
            
            <!-- 订单详情 -->
            <div class="space-y-4">
                <div class="flex justify-between items-center pb-3 border-b border-gray-100">
                    <span class="text-gray-600">订单号</span>
                    <span class="text-gray-800 font-medium">{$order.out_trade_no|default=''}</span>
                </div>
                <div class="flex justify-between items-center pb-3 border-b border-gray-100">
                    <span class="text-gray-600">订单金额</span>
                    <span class="text-red-600 font-bold text-lg">￥{$order.amount|default='0.00'}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">创建时间</span>
                    <span class="text-gray-800">{$order.create_time|default=''}</span>
                </div>
            </div>
        </div>

        <!-- 支付说明 -->
        <div class="bg-blue-50 rounded-lg p-4 mb-6">
            <div class="flex items-start space-x-3">
                <svg class="w-5 h-5 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="text-sm text-blue-700">
                    <p class="font-medium mb-1">温馨提示</p>
                    <p class="text-blue-600">授权后记得点击右上角完成按钮哦</p>
                </div>
            </div>
        </div>

        <!-- 支付按钮 -->
        <div class="text-center">
            <button id="J_btn" class="w-full gradient-bg text-white font-bold py-4 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-indigo-500/30">
                <span class="flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    确认支付
                </span>
            </button>
            <p class="text-xs text-gray-500 mt-3">支付即表示同意《安全租服务协议》</p>
        </div>

        <!-- 加载提示 -->
        <div id="loading" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white p-6 rounded-xl shadow-xl">
                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-600 mx-auto"></div>
                <p class="text-gray-600 mt-3 text-center">支付处理中...</p>
            </div>
        </div>
    </div>

    <script>
        var btn = document.querySelector('#J_btn');
        var loading = document.querySelector('#loading');

        btn.addEventListener('click', function(){
            // 禁用按钮并显示加载状态
            btn.disabled = true;
            loading.classList.remove('hidden');
            
            ap.tradePay({
                orderStr: '{$order.pay_url}'
            }, function(res) {
                // 隐藏加载状态
                loading.classList.add('hidden');
                btn.disabled = false;
                
                if(res.resultCode == '9000'){
                    var alipay_fund_auth_order_app_freeze_response = JSON.parse(res.result).alipay_fund_auth_order_app_freeze_response;
                    var data = JSON.stringify(alipay_fund_auth_order_app_freeze_response);
                    
                    // 显示加载状态
                    loading.classList.remove('hidden');
                    
                    $.ajax({
                        url: 'http://' + window.location.host + '/cashier/index/preauth_pay?data='+data,
                        type: 'POST',
                        success: function(ress){
                            loading.classList.add('hidden');
                            if(ress.code == 1){
                                ap.alert('支付成功', function(){
                                    // 可以在这里添加支付成功后的跳转逻辑
                                });
                            } else {
                                ap.alert(ress.msg || '支付失败，请重试');
                            }
                        },
                        error: function(xhr, status, error) {
                            loading.classList.add('hidden');
                            ap.alert('请求失败，请重试');
                        }
                    });
                } else {
                    // ap.alert(res.resultCode);
                }
            });
        });
    </script>
</body>
</html>