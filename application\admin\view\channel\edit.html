<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-code" data-rule="required" class="form-control" name="row[code]" type="text" value="{$row.code|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('拉单间隔')}:</label>
        <div class="col-xs-6 col-sm-4">
            <input id="c-pull_interval" data-rule="required" class="form-control" name="row[pull_interval]" type="text" value="{$row.pull_interval|htmlentities}">
        </div>
        <span style="line-height: 30px;color:red">单位：秒 0为不限制</span>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('是否启用下浮')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
            {foreach name="preferential_statusList" item="vo"}
            <label for="row[preferential_status]-{$key}"><input id="row[preferential_status]-{$key}" name="row[preferential_status]" type="radio" value="{$key}" {in name="key" value="$row.preferential_status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('下浮金额')}:</label>
        
        <div class="col-xs-4 col-sm-2">
            <input id="c-preferential_min" data-rule="" class="form-control" name="row[preferential_min]" type="text" value="{$row.preferential_min|htmlentities}">
        </div>
        
        <div class="col-xs-4 col-sm-2">
            <input id="c-preferential_max" data-rule="" class="form-control" name="row[preferential_max]" type="text" value="{$row.preferential_max|htmlentities}">
        </div>
        <span style="line-height: 30px;color:red">单位：分 如：1 - 10 表示下浮1分到10分</span>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Notes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-notes" data-rule="required" class="form-control " rows="5" name="row[notes]" cols="50">{$row.notes|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
