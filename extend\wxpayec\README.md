# 微信支付证书配置说明

## 证书文件位置

微信支付API v3需要以下证书文件，请将它们放在 `extend/wxpayec/cart/` 目录下：

### 必需文件

1. **商户API私钥文件**: `apiclient_key.pem`
   - 这是商户的私钥文件，用于生成请求签名
   - 从微信商户平台下载的私钥文件
   - 注意：不是 `apiclient_cert.pem`，而是 `apiclient_key.pem`

2. **微信支付平台公钥文件**: `pub_key.pem`
   - 这是微信支付平台的公钥文件，用于验证响应签名
   - 从微信商户平台下载

### 其他文件说明

- `apiclient_cert.pem`: 商户证书文件（不是私钥）
- `apiclient_cert.p12`: 商户证书的PKCS12格式文件
- `apiclient_key.pem`: **商户私钥文件**（这是我们需要使用的）

## 获取证书文件

1. 登录微信商户平台 (https://pay.weixin.qq.com)
2. 进入 "账户中心" -> "API安全"
3. 下载相应的证书文件
4. 将文件重命名并放到指定目录

## 目录结构

```
extend/
└── wxpayec/
    └── cart/
        ├── apiclient_cert.pem  # 商户证书（不使用）
        ├── apiclient_cert.p12  # 商户证书PKCS12格式（不使用）
        ├── apiclient_key.pem   # 商户私钥（使用这个）
        └── pub_key.pem         # 微信支付公钥（使用这个）
```

## 测试路径

访问以下URL来测试证书文件路径是否正确：
```
http://你的域名/index/wechatpay/testPath
```

## 证书文件格式要求

私钥文件 `apiclient_key.pem` 应该包含以下格式的内容：
```
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
...
-----END PRIVATE KEY-----
```

公钥文件 `pub_key.pem` 应该包含以下格式的内容：
```
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
...
-----END PUBLIC KEY-----
```

## 注意事项

- 证书文件权限应该设置为600（仅所有者可读写）
- 确保文件路径使用正确的目录分隔符（Windows使用\，Linux使用/）
- 代码中已使用ThinkPHP的DS常量来确保跨平台兼容性
- **重要**：使用 `apiclient_key.pem` 作为私钥文件，不是 `apiclient_cert.pem` 