<!DOCTYPE html>
<html>
<head>

    <meta name="description" itemprop="description" content="￥0.01">
    <link href="https://cbu01.alicdn.com/img/ibank/O1CN01G7jrWK1rZutB5kl8V_!!*************-0-cib.jpg" rel="shortcut icon">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0 user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <title>直营商城</title>

    <link rel="stylesheet" type="text/css" href="__CDN__/shop/Mao.min.css">
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/style.css">
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/Mao.diy.css">
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/iconfont.css">
    <script src="__CDN__/shop/jquery-2.1.1.min.js"></script>
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/layer.css">

    <script src="__CDN__/shop/layer.js"></script>
    <script src="__CDN__/shop/Mao.js"></script>
</head>
<body>
<div class="fui-page-group">
    <div class="fui-page fui-page-current page-goods-detail">

        <div class="fui-content basic-block pulldown ">
            <div class="fui-swipe goods-swipe">
                <div class="fui-swipe-wrapper">
                    <div class="fui-swipe-item"><img src="https://cbu01.alicdn.com/img/ibank/O1CN01G7jrWK1rZutB5kl8V_!!*************-0-cib.jpg"></div>
                </div>
            </div>
            <div class="fui-cell-group fui-detail-group" style="margin-top: 0px; margin-bottom: 0px; background: #ffffff;">

                <div class="fui-cell goods-subtitle">
                    <span class="text-danger" style="color: #ef4f4f;">
                                            </span>
                </div>
                <div class="fui-cell">
                    <div class="fui-cell-text price">
                        <span class="text-danger" style="vertical-align: middle; color: #ef4f4f; ">
                            <span style="font-size:1rem;">
                                ￥</span></span>0.01                     </span>
                    </div> <span style="color: #999999;font-size: 12px;margin-top: 5px;">已售:  9</span>
                </div>
                <div class="fui-cell">

                    <div class="fui-cell-text name" style="color: #333333;font-size: 0.9rem;">
                        <img src="__CDN__/shop/bt.png" style="height: 18px;margin-right: 5px;">
                        <span style="margin-top:10px;">港式金汤鲍鱼花胶鸡加热即食鱼胶滋补食材1650g礼盒</span>
                    </div>




                </div>


                <div class="item-serve ">

                    <div><img src="__CDN__/shop/xq.png" style="height: 18px;margin-right: 6px;vertical-align:middle;">
                        <span style="color: #CB7947">基地甄选 · 品质保障 · 售后无忧</span></div>
                    <div class="baozhang">正品保障 · 损坏包赔 · 运费险· 七天无理由</div>

                    <div class="wuliu">物流 <span class="xianhuo">现货</span><span class="songjian "> 闪电送检，24小时内发货，包邮</span>
                    </div>


                </div>

            </div>

            <div class="fui-cell-group">
                <div class="pingjia"> 暂无评价（0）</div>
                <div class=" icon-right right"> </div>
            </div>

            <div class="fui-cell-group">
                <div class="fui-cell">
                    <div>商品详情</div>
                </div>
                <hr>
                <div class="content-block content-images" style="margin: 0.4rem 0.4rem;">
                    <p><img class="desc-img-loaded" src="https://cbu01.alicdn.com/img/ibank/O1CN01G7jrWK1rZutB5kl8V_!!*************-0-cib.jpg" data-lazyload-src="https://cbu01.alicdn.com/img/ibank/O1CN01G7jrWK1rZutB5kl8V_!!*************-0-cib.jpg" alt="undefined"><br><br><img class="desc-img-loaded" src="https://cbu01.alicdn.com/img/ibank/O1CN01ztkjeU1rZutDHmaYK_!!*************-0-cib.jpg" data-lazyload-src="https://cbu01.alicdn.com/img/ibank/O1CN01ztkjeU1rZutDHmaYK_!!*************-0-cib.jpg" alt="undefined"><br><br><img class="desc-img-loaded" src="https://cbu01.alicdn.com/img/ibank/O1CN01VVUdvH1rZutDo02cg_!!*************-0-cib.jpg" data-lazyload-src="https://cbu01.alicdn.com/img/ibank/O1CN01VVUdvH1rZutDo02cg_!!*************-0-cib.jpg" alt="undefined"><br><br><img class="desc-img-loaded" src="https://cbu01.alicdn.com/img/ibank/O1CN01zPLw0c1rZutB5n2Wj_!!*************-0-cib.jpg" data-lazyload-src="https://cbu01.alicdn.com/img/ibank/O1CN01zPLw0c1rZutB5n2Wj_!!*************-0-cib.jpg" alt="undefined"><br><br><img class="desc-img-loaded" src="https://cbu01.alicdn.com/img/ibank/O1CN01Ihnl6K1rZutG7PKgY_!!*************-0-cib.jpg" data-lazyload-src="https://cbu01.alicdn.com/img/ibank/O1CN01Ihnl6K1rZutG7PKgY_!!*************-0-cib.jpg" alt="undefined"><br><br><img class="desc-img-loaded" src="https://cbu01.alicdn.com/img/ibank/O1CN01RPZQrd1rZutDDjda4_!!*************-0-cib.jpg" data-lazyload-src="https://cbu01.alicdn.com/img/ibank/O1CN01RPZQrd1rZutDDjda4_!!*************-0-cib.jpg" alt="undefined"><br><br><img class="desc-img-loaded" src="https://cbu01.alicdn.com/img/ibank/O1CN01prfamf1rZutH7VMDR_!!*************-0-cib.jpg" data-lazyload-src="https://cbu01.alicdn.com/img/ibank/O1CN01prfamf1rZutH7VMDR_!!*************-0-cib.jpg" alt="undefined"></p> </div>
            </div>
        </div>
    </div>
    <div class="fui-navbar bottom-buttons" style="background: #ffffff;">
        <a class="nav-item favorite-item " href="{:url('/')}">
            <span class="icon icon-shop"></span>
            <span class="label" style="color: #000000">店铺</span>
        </a>


        <a class="nav-item external" href="{:url('/index/index/ordersearch')}">
            <span class="icon icon-cart" style="color: #000000"></span>
            <span class="label" style="color: #000000">订单</span>
        </a>


        <a class="nav-item btn buybtn jb" data-type="rb" onclick="aClick()" style="width:5%;">立即购买</a>    </div>
    <div style="display: none;" id="ceshi">
        <div class="fui-modal picker-modal in">
            <div class="option-picker ">
                <div class="option-picker-inner">
                    <div class="option-picker-cell goodinfo">

                        <div class="img" style="z-index: 9999;"><img class="thumb" src="https://cbu01.alicdn.com/img/ibank/O1CN01G7jrWK1rZutB5kl8V_!!*************-0-cib.jpg"></div>

                        <div class="info info-price text-danger">
                            <span>
                                ￥<span class="price">0.01</span>
                            </span>
                        </div>
                        <div style="color:#777;font-size:0.6rem;">
                            库存： 99999件
                        </div>
                        <div style="font-size:0.7rem;">
                            已选择：标配
                        </div>
                    </div>
                    <div class="option-picker-options">

                        <div class="fui-cell-group" style="margin-top:0">
                            <div class="fui-cell">
                                <div class="fui-cell-label">数量</div>
                                <div class="fui-cell-info"></div>
                                <div class="fui-cell-mask noremark">
                                    <div class="fui-number" style="width: auto;">
                                        <div class="minus" id="minus" onclick="minus();">-</div>
                                        <input class="num2" type="tel" name="buynum" value="1" id="num" onkeyup="if(isNaN(value))execCommand(&#39;undo&#39;)">
                                        <div class="plus" id="add" onclick="add();">+</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
        <div class="fui-navbar  " style="display: block;">
            <a href="javascript:;" class="nav-item btn jb" style="" onclick="create();">确定</a>
        </div>
        <script type="text/javascript">
            function create(){
                window.location.href="{:url('/index/index/repair',['id' => 4])}";
                // var loading = layer.open({
                //     type: 3
                //     ,content: '加载中'
                //     ,shade: 'background-color: rgba(0,0,0,.2)'
                //     ,shadeClose:false
                // });
                // $.ajax({
                //     url: 'api/api.php',
                //     type: 'POST',
                //     dataType: 'json',
                //     data: {
                //         mod: "create",
                //         id: "4",
                //         num: $('.num').val(),
                //         // shouji: $("#lianxi").val()
                //     },
                //     success: function (a) {
                //         layer.close(loading);
                //         if (a.code == 0) {
                //             window.location.href="repair.php";
                //         } else {
                //             layer.open({
                //                 content: a.msg
                //                 ,skin: 'msg'
                //                 ,time: 2
                //             });
                //         }
                //     },
                //     error: function() {
                //         layer.close(loading);
                //         layer.open({
                //             content: '~连接服务器失败！'
                //             ,skin: 'msg'
                //             ,time: 2
                //         });
                //     }
                // });
            }
        </script>
    </div>
</div>








<script type="text/javascript">
    function aClick() {
        var html = $('#ceshi').html();
        html=html.replace(/lianxi2/g, "lianxi");
        html=html.replace(/num2/g, "num");
        html=html.replace(/closebtn2/g, "closebtn");

        layer.open ({
            title: false,
            type: 1,
            content: html,
            anim: 'up',
            offset: 'b',
            area: '100%',
            skin: 'demo-class'

        });
    }
    function closebtn(){
        layer.closeAll();
    }
    function add(){
        $('.num').val(parseInt($('.num').val())+1);
    }
    function minus() {
        if ($('.num').val() > 1) {
            $('.num').val(parseInt($('.num').val()) - 1);
        }
    }
</script>


<div class="layui-layer-move"></div>
</body>
</html>