<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use WeChatPay\Builder;
use WeChatPay\Crypto\Rsa;

class Wechatpay extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    // 测试证书文件路径
    public function testPath()
    {
        $merchantPrivateKeyFilePath = ROOT_PATH . 'extend' . DS . 'wxpayec' . DS . 'cart' . DS . 'apiclient_key.pem';
        $platformPublicKeyFilePath = ROOT_PATH . 'extend' . DS . 'wxpayec' . DS . 'cart' . DS . 'pub_key.pem';

        $result = [
            'merchant_private_key' => [
                'path' => $merchantPrivateKeyFilePath,
                'exists' => file_exists($merchantPrivateKeyFilePath),
                'readable' => is_readable($merchantPrivateKeyFilePath),
                'size' => file_exists($merchantPrivateKeyFilePath) ? filesize($merchantPrivateKeyFilePath) : 0,
                'content_preview' => '',
                'format_check' => false,
                'openssl_test' => false,
                'full_content' => ''
            ],
            'platform_public_key' => [
                'path' => $platformPublicKeyFilePath,
                'exists' => file_exists($platformPublicKeyFilePath),
                'readable' => is_readable($platformPublicKeyFilePath),
                'size' => file_exists($platformPublicKeyFilePath) ? filesize($platformPublicKeyFilePath) : 0,
                'content_preview' => '',
                'format_check' => false,
                'openssl_test' => false
            ]
        ];

        // 检查私钥文件内容
        if ($result['merchant_private_key']['exists'] && $result['merchant_private_key']['readable']) {
            $privateKeyContent = file_get_contents($merchantPrivateKeyFilePath);
            $result['merchant_private_key']['full_content'] = $privateKeyContent;
            $result['merchant_private_key']['content_preview'] = substr($privateKeyContent, 0, 100) . '...';
            $result['merchant_private_key']['format_check'] = strpos($privateKeyContent, '-----BEGIN PRIVATE KEY-----') !== false &&
                strpos($privateKeyContent, '-----END PRIVATE KEY-----') !== false;

            // 测试OpenSSL是否能直接加载私钥
            $opensslResult = openssl_pkey_get_private($privateKeyContent);
            $result['merchant_private_key']['openssl_test'] = $opensslResult !== false;
            if ($opensslResult !== false) {
                // 在PHP 8.1+中，openssl_free_key()已被弃用，资源会自动释放
                if (version_compare(PHP_VERSION, '8.1.0', '<')) {
                    openssl_free_key($opensslResult);
                }
            }
        }

        // 检查公钥文件内容
        if ($result['platform_public_key']['exists'] && $result['platform_public_key']['readable']) {
            $publicKeyContent = file_get_contents($platformPublicKeyFilePath);
            $result['platform_public_key']['content_preview'] = substr($publicKeyContent, 0, 100) . '...';
            $result['platform_public_key']['format_check'] = strpos($publicKeyContent, '-----BEGIN PUBLIC KEY-----') !== false &&
                strpos($publicKeyContent, '-----END PUBLIC KEY-----') !== false;

            // 测试OpenSSL是否能直接加载公钥
            $opensslResult = openssl_pkey_get_public($publicKeyContent);
            $result['platform_public_key']['openssl_test'] = $opensslResult !== false;
            if ($opensslResult !== false) {
                // 在PHP 8.1+中，openssl_free_key()已被弃用，资源会自动释放
                if (version_compare(PHP_VERSION, '8.1.0', '<')) {
                    openssl_free_key($opensslResult);
                }
            }
        }

        echo "<h2>微信支付证书文件详细检查结果</h2>";
        echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

        if (!$result['merchant_private_key']['exists']) {
            echo "<p style='color:red;'>❌ 商户私钥文件不存在！请将 apiclient_key.pem 文件放到 {$result['merchant_private_key']['path']}</p>";
        } elseif (!$result['merchant_private_key']['format_check']) {
            echo "<p style='color:red;'>❌ 商户私钥文件格式错误！文件应该包含 -----BEGIN PRIVATE KEY----- 和 -----END PRIVATE KEY-----</p>";
        } elseif (!$result['merchant_private_key']['openssl_test']) {
            echo "<p style='color:red;'>❌ OpenSSL无法加载私钥！可能是私钥内容损坏或格式不正确</p>";
        }

        if (!$result['platform_public_key']['exists']) {
            echo "<p style='color:red;'>❌ 微信支付公钥文件不存在！请将 pub_key.pem 文件放到 {$result['platform_public_key']['path']}</p>";
        } elseif (!$result['platform_public_key']['format_check']) {
            echo "<p style='color:red;'>❌ 微信支付公钥文件格式错误！文件应该包含 -----BEGIN PUBLIC KEY----- 和 -----END PUBLIC KEY-----</p>";
        } elseif (!$result['platform_public_key']['openssl_test']) {
            echo "<p style='color:red;'>❌ OpenSSL无法加载公钥！可能是公钥内容损坏或格式不正确</p>";
        }

        if (
            $result['merchant_private_key']['exists'] && $result['platform_public_key']['exists'] &&
            $result['merchant_private_key']['format_check'] && $result['platform_public_key']['format_check'] &&
            $result['merchant_private_key']['openssl_test'] && $result['platform_public_key']['openssl_test']
        ) {
            echo "<p style='color:green;'>✅ 所有证书文件都存在且格式正确，OpenSSL测试通过</p>";

            // 尝试加载私钥进行测试
            try {
                $merchantPrivateKeyInstance = Rsa::from($merchantPrivateKeyFilePath, Rsa::KEY_TYPE_PRIVATE);
                echo "<p style='color:green;'>✅ 微信支付SDK私钥加载成功！</p>";
            } catch (\Exception $e) {
                echo "<p style='color:red;'>❌ 微信支付SDK私钥加载失败：" . $e->getMessage() . "</p>";

                // 尝试使用文件内容而不是文件路径
                try {
                    $privateKeyContent = file_get_contents($merchantPrivateKeyFilePath);
                    $merchantPrivateKeyInstance = Rsa::from($privateKeyContent, Rsa::KEY_TYPE_PRIVATE);
                    echo "<p style='color:green;'>✅ 使用文件内容加载私钥成功！</p>";
                } catch (\Exception $e2) {
                    echo "<p style='color:red;'>❌ 使用文件内容加载私钥也失败：" . $e2->getMessage() . "</p>";
                }
            }
        }

        // 显示私钥文件的完整内容（用于调试）
        if ($result['merchant_private_key']['full_content']) {
            echo "<h3>私钥文件完整内容：</h3>";
            echo "<pre style='background:#f5f5f5;padding:10px;border:1px solid #ddd;'>" . htmlspecialchars($result['merchant_private_key']['full_content']) . "</pre>";
        }
    }

    // 简单测试微信支付SDK
    public function testSdk()
    {
        try {
            // 商户号
            $merchantId = '1700336359';

            // 读取私钥文件内容
            $merchantPrivateKeyFilePath = ROOT_PATH . 'extend' . DS . 'wxpayec' . DS . 'cart' . DS . 'apiclient_key.pem';
            $merchantPrivateKeyContent = file_get_contents($merchantPrivateKeyFilePath);
            $merchantPrivateKeyInstance = Rsa::from($merchantPrivateKeyContent, Rsa::KEY_TYPE_PRIVATE);

            // 「商户API证书」的「证书序列号」
            $merchantCertificateSerial = '647DEAC3CC0755AFCFC24AD0E86606CBD9A205F9';

            // 读取公钥文件内容
            $platformPublicKeyFilePath = ROOT_PATH . 'extend' . DS . 'wxpayec' . DS . 'cart' . DS . 'pub_key.pem';
            $platformPublicKeyContent = file_get_contents($platformPublicKeyFilePath);
            $platformPublicKeyInstance = Rsa::from($platformPublicKeyContent, Rsa::KEY_TYPE_PUBLIC);

            // 「微信支付公钥」的「微信支付公钥ID」
            $platformPublicKeyId = 'PUB_KEY_ID_0117003363592025062500451560001200';

            // 构造一个 APIv3 客户端实例
            $instance = Builder::factory([
                'mchid'      => $merchantId,
                'serial'     => $merchantCertificateSerial,
                'privateKey' => $merchantPrivateKeyInstance,
                'certs'      => [
                    $platformPublicKeyId => $platformPublicKeyInstance,
                ]
            ]);

            echo "<h2>微信支付SDK测试结果</h2>";
            echo "<p style='color:green;'>✅ 微信支付SDK初始化成功！</p>";
            echo "<p>商户号: {$merchantId}</p>";
            echo "<p>证书序列号: {$merchantCertificateSerial}</p>";
            echo "<p>公钥ID: {$platformPublicKeyId}</p>";

            return json(['code' => 1, 'msg' => '微信支付SDK初始化成功']);
        } catch (\Exception $e) {
            echo "<h2>微信支付SDK测试结果</h2>";
            echo "<p style='color:red;'>❌ 微信支付SDK初始化失败：" . $e->getMessage() . "</p>";

            return json(['code' => 0, 'msg' => '微信支付SDK初始化失败：' . $e->getMessage()]);
        }
    }

    public function index()
    {
        // 商户号
        $merchantId = '1700336359';

        // 从本地文件中加载「商户API私钥」，用于生成请求的签名
        $merchantPrivateKeyFilePath = ROOT_PATH . 'extend' . DS . 'wxpayec' . DS . 'cart' . DS . 'apiclient_key.pem';

        // 检查私钥文件是否存在
        if (!file_exists($merchantPrivateKeyFilePath)) {
            throw new \Exception('商户私钥文件不存在: ' . $merchantPrivateKeyFilePath);
        }

        // 读取私钥文件内容
        $merchantPrivateKeyContent = file_get_contents($merchantPrivateKeyFilePath);
        $merchantPrivateKeyInstance = Rsa::from($merchantPrivateKeyContent, Rsa::KEY_TYPE_PRIVATE);

        // 「商户API证书」的「证书序列号」
        $merchantCertificateSerial = '647DEAC3CC0755AFCFC24AD0E86606CBD9A205F9';

        // 从本地文件中加载「微信支付公钥」，用来验证微信支付应答的签名
        $platformPublicKeyFilePath = ROOT_PATH . 'extend' . DS . 'wxpayec' . DS . 'cart' . DS . 'pub_key.pem';

        // 检查公钥文件是否存在
        if (!file_exists($platformPublicKeyFilePath)) {
            throw new \Exception('微信支付公钥文件不存在: ' . $platformPublicKeyFilePath);
        }

        // 读取公钥文件内容
        $platformPublicKeyContent = file_get_contents($platformPublicKeyFilePath);
        $platformPublicKeyInstance = Rsa::from($platformPublicKeyContent, Rsa::KEY_TYPE_PUBLIC);

        // 「微信支付公钥」的「微信支付公钥ID」
        // 需要在 商户平台 -> 账户中心 -> API安全 查询
        $platformPublicKeyId = 'PUB_KEY_ID_0117003363592025062500451560001200';

        // 构造一个 APIv3 客户端实例(微信支付公钥模式)
        $instance = Builder::factory([
            'mchid'      => $merchantId,
            'serial'     => $merchantCertificateSerial,
            'privateKey' => $merchantPrivateKeyInstance,
            'certs'      => [
                $platformPublicKeyId => $platformPublicKeyInstance,
            ],
            // HTTP客户端配置，禁用SSL证书验证（仅用于开发环境）
            'verify' => false,
            'timeout' => 30,
        ]);

        try {
            $response = $instance->v3->pay->partner->transactions->h5->post([
                'json' => [
                    'sp_appid'       => 'wx0ee6670e7b952779',
                    'sp_mchid'       => '1700336359',
                    // 'sub_appid'      => 'wx0ee6670e7b952779',
                    'sub_mchid'      => '1720729341',
                    'settle_info'    => [
                        'profit_sharing' => true,
                        'subsidy_amount' => 10,
                    ],
                    'description'    => 'Image形象店-深圳腾大-QQ公仔',
                    'attach'         => '自定义数据',
                    'out_trade_no'   => '1217752501201407033233368018',
                    'amount'         => [
                        'total'    => 100,
                        'currency' => 'CNY',
                    ],
                    'time_expire'    => '2018-06-08T10:34:56+08:00',
                    'notify_url'     => 'https://www.weixin.qq.com/wxpay/pay.php',
                    'goods_tag'      => 'WXG',
                    'limit_pay'      => ['no_balance'],
                    'support_fapiao' => true,
                    'detail'         => [
                        'cost_price'   => 608800,
                        'invoice_id'   => '微信123',
                        'goods_detail' => [[
                            'merchant_goods_id'  => '1001',
                            'wechatpay_goods_id' => '1001',
                            'goods_name'         => 'iPhoneX 256G',
                            'quantity'           => 1,
                            'unit_price'         => 828800,
                        ],],
                    ],
                    'scene_info'     => [
                        'payer_client_ip' => '*************',
                        'device_id'       => '013467007045764',
                        'store_info'      => [
                            'id'        => '0001',
                            'name'      => '腾讯大厦分店',
                            'area_code' => '440305',
                            'address'   => '广东省深圳市南山区科技中一道10000号',
                        ],
                        'h5_info'         => [
                            'type'         => 'iOSAndroidWap',
                            'app_name'     => '王者荣耀',
                            'app_url'      => 'https://pay.qq.com',
                            'bundle_id'    => 'com.tencent.wzryiOS',
                            'package_name' => 'com.tencent.tmgp.sgame',
                        ],
                    ],
                    'payer'          => [
                        'identity' => [
                            'type'   => 'IDCARD',
                            'number' => '6B46824C852FA29AAC3DCE6BFD852E27',
                            'name'   => '6B46824C852FA29AAC3DCE6BFD852E27',
                        ],
                    ],
                ],
                'headers' => [
                    'Wechatpay-Serial' => 'PUB_KEY_ID_0117003363592025062500451560001200',
                ],
            ]);
            print_r(json_decode((string) $response->getBody(), true));
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            // 处理HTTP客户端错误（如403、404等）
            $response = $e->getResponse();
            $errorBody = json_decode((string) $response->getBody(), true);


            if (isset($errorBody['message'])) {
                echo $errorBody['message'];
            }
        } catch (\Exception $e) {
            echo "<h2>微信支付API调用失败</h2>";
            echo "<p style='color:red;'>错误信息：" . $e->getMessage() . "</p>";
        }
    }
}
