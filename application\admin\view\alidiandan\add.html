<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('支付宝账号')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account_identity" data-rule="required" class="form-control" name="row[account_identity]" type="text" value="">
        </div>
    </div> -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('店铺名称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('店铺链接')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-game_url" data-rule="required" class="form-control" name="row[game_url]" type="text" value="">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('最小金额')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-min_price" data-rule="required" class="form-control" name="row[min_price]" type="number" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('最大金额')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-max_price" data-rule="required" class="form-control" name="row[max_price]" type="number" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('每日收款上限')}:</label>
        <div class="col-xs-6 col-sm-4">
            <input id="c-maximum" data-rule="required" class="form-control" name="row[maximum]" type="number" value="0">
        </div>
        <span style="line-height: 30px;color:red">0为不限制</span>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('异常笔数限制')}:</label>
        <div class="col-xs-6 col-sm-4">
            <input id="c-nopay_limit" data-rule="required" class="form-control" name="row[nopay_limit]" type="number" value="0">
        </div>
        <span style="line-height: 30px;color:red">0为不限制</span>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('收款笔数限制')}:</label>
        <div class="col-xs-6 col-sm-4">
            <input id="c-pay_limit" data-rule="required" class="form-control" name="row[pay_limit]" type="number" value="0">
        </div>
        <span style="line-height: 30px;color:red">0为不限制</span>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('通道编码')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-channel" data-rule="required" class="form-control" name="row[channel]" type="text" value="9008">
        </div>
    </div>
    <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="normal"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>
        </div>
    </div> -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('备注')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-notes" data-rule="required" class="form-control" name="row[notes]" type="text" value="">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
