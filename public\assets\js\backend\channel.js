define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'channel/index' + location.search,
                    add_url: 'channel/add',
                    edit_url: 'channel/edit',
                    del_url: 'channel/del',
                    multi_url: 'channel/multi',
                    import_url: 'channel/import',
                    table: 'channel',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'code', title: __('Code'), operate: 'LIKE'},
                        {field: 'notes', title: __('备注'), operate: 'LIKE'},
                        {field: 'preferential_min', title: __('下浮金额'),formatter: function(value, row, index){
                            return value + ' - ' + row.preferential_max;
                        }},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status normal'),"0":__('Status hidden')}, formatter: Table.api.formatter.status},
                        {field: 'preferential_status', title: __('是否启用下浮'), searchList: {"1":__('启用'),"0":__('不启用')}, formatter: Table.api.formatter.status},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
