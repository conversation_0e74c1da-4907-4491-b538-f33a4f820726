<?php

namespace app\admin\model;

use think\Model;


class Order extends Model
{

    // 表名
    protected $name = 'order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 追加属性
    protected $append = [

    ];

    protected $auto = ['callback_url'];
    protected $insert = ['callback_url'];
    protected $update = ['callback_url'];

    protected function setCallbackUrlAttr($value)
    {
        return urldecode($value);
    }

    public function merchants()
    {
        return $this->belongsTo('app\admin\model\Merchants', 'merchants_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function admin()
    {
        return $this->belongsTo('app\admin\model\Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
