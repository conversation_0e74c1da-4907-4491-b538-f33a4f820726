# 微信支付证书文件放置说明

## 问题说明
当前目录为空，需要放置微信支付所需的证书文件。

## 需要放置的文件

1. **apiclient_key.pem** - 商户API私钥文件
2. **pub_key.pem** - 微信支付平台公钥文件

## 获取证书文件的步骤

1. 登录微信商户平台：https://pay.weixin.qq.com
2. 进入 "账户中心" -> "API安全"
3. 下载以下文件：
   - 商户API私钥文件（重命名为 `apiclient_key.pem`）
   - 微信支付平台公钥文件（重命名为 `pub_key.pem`）

## 文件格式要求

### apiclient_key.pem 格式：
```
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
（这里是私钥内容）
-----END PRIVATE KEY-----
```

### pub_key.pem 格式：
```
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
（这里是公钥内容）
-----END PUBLIC KEY-----
```

## 测试方法

放置文件后，访问以下URL测试：
```
http://你的域名/index/wechatpay/testPath
```

这将显示文件是否存在和可读状态。

## 注意事项

- 确保文件权限正确（建议600）
- 文件名必须完全匹配：`apiclient_key.pem` 和 `pub_key.pem`
- 不要使用 `apiclient_cert.pem`，这是证书文件，不是私钥文件 