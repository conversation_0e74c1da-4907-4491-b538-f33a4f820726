<?php

namespace app\common\library;

use app\admin\model\Admin;
use fast\Http;
use think\Db;

class Order
{

    /**
     * 参数排序
     */
    public static function ascii($params = array()){
        unset($params['undefined']);
        unset($params['sign']);
        //ksort()对数组按照键名进行升序排序
        ksort($params);
        //reset()内部指针指向数组中的第一个元素
        reset($params);
        $str = http_build_query($params, '', '&');
        return $str;
    }


    //创建订单号
    public static function createUniqueNo()
    {
        #yCode 可以按照自己的实际情况调整
        $yCode = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I','J','I','K','L','Q','W','R','Y','U');
        $order_sn = $yCode[intval(date('Y')) - 2011] . strtoupper(dechex(date('m'))) .date('d') . substr(time(), -5) . substr(microtime(), 2, 5) . sprintf('%02d', rand(0, 99));

        return $order_sn;
    }

    //创建订单号
    public static function createUniqueNos()
    {
        #yCode 可以按照自己的实际情况调整
        $yCode = array('T','P','R');
        $order_sn = $yCode[array_rand($yCode)] . strtoupper(dechex(date('m'))) . date('d') .substr(time(), -5) . substr(microtime(), 2, 5) . sprintf('%02d', rand(0, 99));
        return $order_sn;
    }

    /**
     * 扣除抖币额度
     */
//    public static function deductAwemeGold($uid, $amount)
//    {
//        Db::startTrans();
//        try {
//            $hexiao = Admin::where('id', $uid)->lock(true)->find();
//            $hexiao->aweme_gold = $hexiao->aweme_gold - $amount;
//            $hexiao->save();
//
//            $parent = Admin::where('id', $hexiao->parent_id)->lock(true)->find();
//            $parent->aweme_gold = $parent->aweme_gold - $amount;
//            $parent->save();
//
//            if ($parent->parent_id != 2) {
//                $tenant = Admin::where('id', $parent->parent_id)->lock(true)->find();
//                $tenant->aweme_gold = $tenant->aweme_gold - $amount;
//                $tenant->save();
//            }
//            Db::commit();
//            return true;
//        } catch (\Exception $e) {
//            Db::rollback();
//            trace($e->getMessage(), 'error');
//            return false;
//        }
//
//    }


    /**
     * 给商户回调
     */
    // public static function merchantsCallback($url, $key, $out_trade_no, $amount, $channel_code, $status)
    // {
    //     $params = [
    //         "out_trade_no"=>$out_trade_no,
    //         "amount"=>$amount,
    //         "channel_code"=>$channel_code,
    //         "status"=>$status
    //     ];
    //     $params['sign'] = md5(Order::ascii($params) . "&key=" . $key);
    //     $res = Http::post($url, $params);
    //     if ($res == 'SUCCESS' or $res == 'success') {
    //         return true;
    //     }
    //     trace('请求 :'.json_encode($params),'error');
    //     trace('商户返回:'.$res,'error');
    //     return false;
    // }
    
    /**
     * 给商户回调（带自动重试机制）
     * 
     * @param string $url 回调地址
     * @param string $key 签名密钥
     * @param string $out_trade_no 商户订单号
     * @param float $amount 金额
     * @param string $channel_code 渠道代码
     * @param string $status 状态
     * @param int $maxRetries 最大重试次数（默认3次）
     * @param int $retryDelay 重试间隔毫秒数（默认1000ms）
     * @return bool 是否回调成功
     */
    public static function merchantsCallback($url, $key, $out_trade_no, $amount, $channel_code, $status, $maxRetries = 3, $retryDelay = 1000)
    {
        $params = [
            "out_trade_no" => $out_trade_no,
            "amount" => $amount,
            "channel_code" => $channel_code,
            "status" => $status
        ];
        $params['sign'] = md5(Order::ascii($params) . "&key=" . $key);
        
        $retryCount = 0;
        $lastError = null;
        
        while ($retryCount < $maxRetries) {
            try {
                $res = Http::post($url, $params);
                
                if ($res == 'SUCCESS' || strtolower($res) == 'success') {
                    return true;
                }
                
                $lastError = $res;
                trace("回调请求失败（尝试 {$retryCount}/{$maxRetries}）: " . json_encode($params), 'error');
                trace("商户返回: {$res}", 'error');
                
            } catch (Exception $e) {
                $lastError = $e->getMessage();
                trace("回调请求异常（尝试 {$retryCount}/{$maxRetries}）: " . $e->getMessage(), 'error');
            }
            
            $retryCount++;
            if ($retryCount < $maxRetries) {
                usleep($retryDelay * 1000); // 转换为微秒
            }
        }
        
        // 所有重试都失败后记录错误
        trace('最终回调失败: '.json_encode($params), 'error');
        trace('最后错误: '.$lastError, 'error');
        
        return false;
    }
}