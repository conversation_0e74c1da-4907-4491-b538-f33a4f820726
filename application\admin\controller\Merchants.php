<?php

namespace app\admin\controller;

use app\admin\model\Order;
use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * Merchant
 *
 * @icon fa fa-circle-o
 */
class Merchants extends Backend
{
    protected $dataLimit = "personal";
    protected $dataLimitField = "admin_id";
    /**
     * Merchants模型对象
     * @var \app\admin\model\Merchants
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Merchants;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 获取数据限制的管理员ID
     * 禁用数据限制时返回的是null
     * @return mixed
     */
    protected function getDataLimitAdminIds()
    {
        if (!$this->dataLimit) {
            return null;
        }
        if ($this->auth->isSuperAdmin()) {
            return null;
        }
        if ($this->auth->id == 2) {
            return null;
        }
        $adminIds = [];
        if (in_array($this->dataLimit, ['auth', 'personal'])) {
            $adminIds = $this->dataLimit == 'auth' ? $this->auth->getChildrenAdminIds(true) : [$this->auth->id];
        }
        return $adminIds;
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['admin'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->getRelation('admin')->visible(['nickname']);
                $row['today'] = Order::where('merchants_id',$row['id'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'today')
                    ->sum('amount');
                $row['today'] = number_format($row['today'],2);
                $row['yesterday'] = Order::where('merchants_id',$row['id'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'yesterday')
                    ->sum('amount');
                $row['yesterday'] = number_format($row['yesterday'],2);
                $row['beforeday'] = Order::where('merchants_id',$row['id'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'between',[date('Y-m-d',time()-2*24*3600),date('Y-m-d',time()-1*24*3600)],)
                    ->sum('amount');
                $row['beforeday'] = number_format($row['beforeday'],2);
            }
            unset($row);
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        //  生成商户密钥和商户编码逻辑开始
        $maxId = $this->model->where('id','>','0')->limit(1)->order('id','desc')->select();
        if (count($maxId)>0) {
            $maxId = $maxId[0]->id + 1;
        } else {
            $maxId = $this->model->max('id');
        }
        $userLongId = 100000 + $this->auth->id;
        $params['code'] = "MC".$userLongId.'_'.$maxId;
        $rd = rand(9,9999);
        $params['key'] = md5($params['code'].$rd);
        //  生成商户密钥和商户编码逻辑结束
        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

}
