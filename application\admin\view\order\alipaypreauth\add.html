<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mc_order')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mc_order" data-rule="required" class="form-control" name="row[mc_order]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lc_order')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lc_order" data-rule="required" class="form-control" name="row[lc_order]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Db_order')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-db_order" data-rule="required" class="form-control" name="row[db_order]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" data-rule="required" class="form-control" step="0.01" name="row[amount]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Merchants_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-merchants_id" data-rule="required" data-source="merchants/index" class="form-control selectpage" name="row[merchants_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tenant_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tenant_id" data-rule="required" data-source="tenant/index" class="form-control selectpage" name="row[tenant_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Author_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-author_id" data-rule="required" data-source="author/index" class="form-control selectpage" name="row[author_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_ip')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_ip" data-rule="required" class="form-control" name="row[create_ip]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Useragent')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-useragent" data-rule="required" class="form-control " rows="5" name="row[useragent]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Channel_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-channel_code" data-rule="required" class="form-control" name="row[channel_code]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_url" data-rule="required" class="form-control" name="row[pay_url]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Callback_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-callback_url" data-rule="required" class="form-control" name="row[callback_url]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Callback_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-callback_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[callback_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Account_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account_id" data-rule="required" data-source="account/index" class="form-control selectpage" name="row[account_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
