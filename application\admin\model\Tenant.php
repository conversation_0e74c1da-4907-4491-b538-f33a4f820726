<?php

namespace app\admin\model;

use think\Model;
use think\Session;

class Tenant extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'fa_admin';

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 隐藏字段
    protected $hidden = ['password', 'salt', 'token', 'mobile', 'email'] ;
    /**
     * 重置用户密码
     * <AUTHOR>
     */
    public function resetPassword($uid, $NewPassword)
    {
        $passwd = $this->encryptPassword($NewPassword);
        $ret = $this->where(['id' => $uid])->update(['password' => $passwd]);
        return $ret;
    }

    // 密码加密
    protected function encryptPassword($password, $salt = '', $encrypt = 'md5')
    {
        return $encrypt($password . $salt);
    }

    public function parent()
    {
        return $this->belongsTo('Tenant', 'parent_id')->setEagerlyType(0);
    }
//
//    public function roles()
//    {
//        return $this->belongsToMany('app\admin\model\AuthGroup', 'auth_group_access', 'group_id', 'uid');
//    }
}
