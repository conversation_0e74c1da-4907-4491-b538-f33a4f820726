<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>支付宝在线付款</title>
		<meta name="referrer" content="never">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
	</head>
	<link rel="stylesheet" type="text/css" href="__CDN__/pipei/pay.css">
	<body>
		<div id="main">
			<div class="left">
				<div class="make">
					<p>
						<img src="__CDN__/pipei/alipay-logo.png" alt="" style="height:30px;">
					</p>
					<p>{$order.local_trade_no}</p>
					<p class="money" id="price" style="font-weight:bold; color:red">正在付款 {$order.amount}</p>
					<center style="padding: 10px 10px 0px 10px">
						<p class="qrcode" id="qrcode">
						    <div id="qrcodeNode" class="" style="display:none"></div>
						    <div class="qr-image" id="qrcode"></div>
                            <canvas width="256" height="256" style="display: none;"></canvas>
							<img id="qrcode_load" src="__CDN__/pipei/loading.gif" style="display: block;">
							</img>
						</p>
					</center>
					
					<center style="padding: 0px 10px 20px 10px">
					    <a id="h5url_1"></a>
	             	</center>

					<div class="info">
					    <p id="matching">订单正在匹配中,请稍等...</p>
						<p id="divTime">-</p>
						<p>订单号：{$order.local_trade_no}</p>
						<p>请打开使用支付宝扫一扫</p>
					</div>
				</div>
			</div>
			<div class="right">
				<img src="__CDN__/pipei/alipay-sys.png" alt="">
			</div>
		</div>
	</body>
</html>
<script type="text/javascript" src="__CDN__/pipei/qrcode.min.js"></script>
<script type="text/javascript" src="__CDN__/pipei/jquery.min.js"></script>
<script type="text/javascript" src="__CDN__/pipei/layer.js"></script>

<script type="text/javascript">

	var intDiff = 0;//倒计时总秒数量

	function timer(intDiff,msg = ''){
	    
	    if(intDiff == 0){
	        order_status_html(msg);
	        return;
	    }
	    
	    window.setInterval(function(){
	          var day=0,
	              hour=0,
	              minute=0,
	              second=0;//时间默认值       
	          if(intDiff > 0){
	          	day = Math.floor(intDiff / (60 * 60 * 24));
	              hour = Math.floor(intDiff / (60 * 60)) - (day * 24);
	              minute = Math.floor(intDiff / 60) - (day * 24 * 60) - (hour * 60);
	             second = Math.floor(intDiff) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60);
	          }
		      if (minute <= 9) minute = '0' + minute;
		      if (second <= 9) second = '0' + second;
		      if (hour <= 0 && minute <= 0 && second <= 0) {
		          order_status_html();
		      }else{
		      	  $("#divTime").html("支付倒计时:<small style='color:red; font-size:18px'> " + minute + " </small>分<small style='color:red; font-size:18px'> " + second + " </small>秒,失效勿付");
		      }
	          intDiff--
		}, 1000);
	}
	
	
	function order_status_html(msg = "该订单已过期,请勿再付"){
	    $('#qrcode_load').remove();//隐藏等待图片
	    $("#matching").html("<small style='color:red; font-size:15px'>"+msg+"</small>");
	    if(msg == '该订单已过期,请勿再付'){
	        $("#divTime").html("<small style='color:red; font-size:18px'>订单过期,关闭网页,重新下单</small>");
	        $("#qrcode").html('<img id="qrcode_load" src="__CDN__/qrcode_timeout.png">');//输出过期二维码提示图片
	    }else{
	        $("#divTime").html("<small style='color:red; font-size:18px'>匹配失败,关闭网页,重新下单</small>");
		    $("#qrcode").html('<img id="qrcode_load" src="__CDN__/pipei_out.png?t=1">');//输出匹配失败二维码提示图片
	    }
	    
		
	}
	
   
	add_order();
	
	//周期监听 
	orderlst = window.setInterval(function () {
// 		order();
	}, 3000); 
	
	
    function add_order(){
		$.get("/cashier/index/getOrderInfo",{trade_no: "{$order.local_trade_no}",channel: "{$order.channel_code}"},function(result){
			if(result.code == '1'){
			    result_info(result);
			}else{
			    setTimeout(function () {
                  add_order();
                }, 1000);
			}
		},"json");
	}
	
	function result_info(result){

	    $("#matching").html("<small style='color:green; font-size:15px'>"+result.msg+"</small>");
       
        intDiff = parseInt(300);//倒计时总秒数量

        timer(intDiff,result.msg);
       	var qrcode = new QRCode("qrcodeNode", {
             text: result.url,
             width: 210,
             height: 210,
             colorDark: "#000000",
             colorLight: "#ffffff",
             correctLevel: QRCode.CorrectLevel.H
        });
       	
        var mycanvas = document.getElementsByTagName('canvas')[0];
        var imghtml = convertCanvasToimage(mycanvas);
        $("#qrcode").append(imghtml);
        $('#qrcode_load').remove();//隐藏等待图片
        open_url(result.url);
        
        if(window.navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)){
        	$("#h5url_1").html('<a type="button" href="'+result.data.pay_url+'" class="btn btn-lg btn-block btn-success" style="font-size:13px" target="_blank">点击唤醒支付</a>');//H5按钮
        }
 
	}
	
	function convertCanvasToimage(canvas){
        var img = new Image();
        img.src = canvas.toDataURL("image/png");
        return img;
    }
	
// 	function order(){
// 		$.get("__CDN__/pipei/order/query_order",{trade_no: "u_20250506221127942486729"},function(result){
// 			if(result.code == '200' && result.msg == '支付成功'){
// 		        $("#matching").html("<small style='color:green; font-size:15px'>支付成功,即将跳转</small>");
// 		     	$("#divTime").html("-");
// 				$("#qrcode").html('<img id="qrcode_load" src="__CDN__/pipei/static/pay/alipay/image/pay_ok.png">');//输出过期二维码提示图片
				
//  				//回调页面
//          		window.clearInterval(orderlst);
// 				layer.msg('支付成功，即将跳转...');
// 				setTimeout(function() {
//  			     	window.location.href = result.data.return_url;
//                 }, 3000); // 延迟时间为3秒
// 			}
// 		},"json");
		
// 	}

</script>

</body>
</html>
<script>
	function open_url(url){
	    if (window.navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)) {
	        window.location.href = url;
	    	layer.msg('正在打开支付,请稍等...',  { time: 3000 });
	    }
	}
</script>