
define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'qrcode'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            $(".btn-edit").data("area", ["80%", "80%"]);//编辑
            $(".btn-add").data("area", ["80%", "80%"]); //添加
            $(".btn-paylimit").data("area", ["80%", "80%"]); //批量设置收款笔数

            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'alishopcode/index' + location.search,
                    add_url: 'alishopcode/add',
                    edit_url: 'alishopcode/edit',
                    del_url: 'alishopcode/del',
                    paylimit_url: 'alishopcode/paylimit',
                    multi_url: 'alishopcode/multi',
                    import_url: 'alishopcode/import',
                    table: 'alishopcode',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('店铺名称')},
                        {field: 'account_identity', title: __('支付宝账号')},
                        
                        {field: 'today_rate', title: __('今日成率%'), formatter: function (v) {
                                return "<span class='btn btn-success-light'>"+v+"</span>";
                            }},
                        {field: 'today', title: __('今日充值'),formatter: function (v) {
                                return "<span class='btn btn-success'>"+v+"</span>";
                            }},
                        {field: 'yesterday', title: __('昨日充值')},
                        {field: 'beforeday', title: __('前日充值')},
                        {field: 'min_price', title: __('最小金额')},
                        {field: 'max_price', title: __('最大金额')},
                        {field: 'maximum', title: __('并发限制')},
                        {field: 'pay_limit', title: __('收款笔数限制')},
                        {field: 'channel', title: __('通道编码'),formatter:Table.api.formatter.flag,custom:{'': 'info'}},
                        {field: 'status', title: __('状态'), searchList: {"normal":__('正常'),"hidden":__('禁用')}, formatter: Table.api.formatter.toggle,yes: 'normal',
                            no: 'hidden'},
                        {field: 'notes', title: __('备注')},
                        // {field: 'statusinfo', title: __('异常状态')},
                        {field: 'admin.nickname', title: __('创建人'), formatter: Table.api.formatter.search},
                        {field: 'pulltime', title: __('拉单时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons :[
                                // {
                                //     name: 'test',
                                //     text: '测试拉单',
                                //     extend:'data-area=["600px","300px"]',
                                //     classname: 'btn btn-xs btn-info btn-click',
                                //     click: function (data,ret) {
                                //         Fast.api.layer.open({
                                //             type:2,
                                //             title:'测试拉单',
                                //             area:["350px","470px"],
                                //             content:"alishopcode/test?ids="+ret.ids
                                //         });
                                //     }
                                // }
                            ],

                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 批量设置收款笔数
            $(".btn-paylimit").on('click', function (){
                // Fast.config.openArea = ['800px','600px']; //设置弹窗宽高,注意这里是全局调整
                var temp=Table.api.selectedids($("#table"));
                Fast.api.open($.fn.bootstrapTable.defaults.extend.paylimit_url+'?ids='+temp,'批量设置收款笔数');
            });

            table.on('post-body.bs.table', function () {
                $(".btn-editone").data("area", ["80%", "80%"]);
                $(".btn-addone").data("area", ["80%", "80%"]);
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        paylimit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        },
        // test: {
        //     bindevent: function () {
        //         Form.api.bindevent($("form[role=form]"));
        //     }
        // }
    };
    return Controller;
});
