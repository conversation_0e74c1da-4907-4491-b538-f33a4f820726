<?php

namespace app\admin\model;

use think\Model;


class Channel extends Model
{

    

    

    // 表名
    protected $name = 'channel';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text',
        'preferential_status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['1' => __('Status normal'), '0' => __('Status hidden')];
    }

    public function getPreferentialStatusList()
    {
        return ['1' => __('启用'), '0' => __('不启用')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getPreferentialStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['preferential_status']) ? $data['preferential_status'] : '');
        $list = $this->getPreferentialStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


}
