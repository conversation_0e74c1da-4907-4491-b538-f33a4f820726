<?php

class ChineseUsernameGenerator {
    /**
     * 生成真实感用户名函数，符合中国用户取名习惯
     * 只包含字母和数字
     * @param bool $withNumber 是否包含数字
     * @return string 生成的用户名
     */
    public static function generate_username($withNumber = true)
    {
        // 常见中国姓氏拼音
        $surnames = [
            'wang', 'li', 'zhang', 'liu', 'chen', 'yang', 'huang', 'zhao', 'wu', 'zhou',
            'xu', 'sun', 'ma', 'zhu', 'hu', 'guo', 'he', 'gao', 'lin', 'luo',
            'zheng', 'liang', 'xie', 'song', 'tang', 'xu', 'ye', 'han', 'feng', 'yu'
        ];
        
        // 常见名字组合部分
        $namePrefix = [
            'xiao', 'da', 'lao', 'xiong', 'mei', 'hai', 'tian', 'ming', 'yun', 'jia',
            'hui', 'xin', 'yi', 'jun', 'xiang', 'yan', 'qiang', 'wei', 'yu', 'fei'
        ];
        
        // 常见名字后缀
        $nameSuffix = [
            'feng', 'long', 'hui', 'tao', 'jie', 'jun', 'yu', 'ming', 'tian', 'hong',
            'wei', 'cheng', 'jian', 'hao', 'le', 'xi', 'er', 'qian', 'fang', 'ping'
        ];
        
        // 常见用户名类型
        $types = [
            // 姓氏+名字组合
            function() use ($surnames, $nameSuffix) {
                return $surnames[array_rand($surnames)] . $nameSuffix[array_rand($nameSuffix)];
            },
            // 前缀+姓氏
            function() use ($namePrefix, $surnames) {
                return $namePrefix[array_rand($namePrefix)] . $surnames[array_rand($surnames)];
            },
            // 姓氏+常见单字
            function() use ($surnames) {
                $singles = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'q', 'r', 's', 't', 'w', 'x', 'y', 'z'];
                return $surnames[array_rand($surnames)] . $singles[array_rand($singles)];
            },
            // 双字名
            function() use ($nameSuffix) {
                return $nameSuffix[array_rand($nameSuffix)] . $nameSuffix[array_rand($nameSuffix)];
            },
            // 常见英文名（中国人常用）
            function() {
                $englishNames = ['andy', 'tony', 'eric', 'jack', 'kevin', 'mike', 'tom', 'david', 'alex', 'sam', 
                                'lucy', 'lily', 'amy', 'jenny', 'cathy', 'emma', 'grace', 'ivy', 'ruby', 'zoe'];
                return $englishNames[array_rand($englishNames)];
            }
        ];
        
        // 随机选择一种类型生成用户名基础部分
        $username = $types[array_rand($types)]();
        
        // 如果需要数字，添加常见数字组合
        if ($withNumber) {
            // 决定是否添加数字（80%概率）
            if (mt_rand(1, 100) <= 80) {
                $numberTypes = [
                    // 年份，如1990, 2000, 98等
                    function() {
                        $yearTypes = [
                            // 完整年份
                            function() {
                                return mt_rand(1980, 2010);
                            },
                            // 年份后两位
                            function() {
                                return mt_rand(0, 10) < 7 ? 
                                       sprintf('%02d', mt_rand(80, 99)) : // 80-99年出生较多
                                       sprintf('%02d', mt_rand(0, 20));   // 00-20年出生较少
                            }
                        ];
                        return $yearTypes[array_rand($yearTypes)]();
                    },
                    // 幸运数字，如520, 666, 888等
                    function() {
                        $luckyNumbers = ['520', '521', '1314', '666', '888', '168', '518', '555', '333', '777'];
                        return $luckyNumbers[array_rand($luckyNumbers)];
                    },
                    // 1-3位随机数字
                    function() {
                        $digits = mt_rand(1, 3);
                        return mt_rand(pow(10, $digits-1), pow(10, $digits)-1);
                    }
                ];
                
                $username .= $numberTypes[array_rand($numberTypes)]();
            }
        }
        
        // 确保用户名长度在5-15个字符之间
        if (strlen($username) < 5) {
            // 如果太短，添加随机数字
            $username .= mt_rand(100, 999);
        } elseif (strlen($username) > 15) {
            // 如果太长，截断
            $username = substr($username, 0, 15);
        }
        
        return $username;
    }
}
