<?php
namespace Payment;
use think\Db;
use think\Request;

Class Service {

    /**
     * 获取资金授权冻结订单信息
     * @param string $appid 支付宝appid
     * @param string $alipay_public_key 支付宝公钥
     * @param string $app_private_key 支付宝私钥
     * @param string $out_trade_no 商户订单号
     * @param string $amount 订单金额
     */
    public static function getPreAuthFreezeOrderInfo($appid,$alipay_public_key,$app_private_key,$out_trade_no,$amount)
    {
        $alipay_config = [
            'app_id' => $appid,
            'cert_mode' => 0,
            'alipay_public_key' => $alipay_public_key,
            'app_private_key' => $app_private_key,
            'sign_type' => "RSA2",
            'charset' => "UTF-8",
        ];
        $bizContent = [
            'out_order_no' => $out_trade_no, //商户订单号
            'out_request_no' => $out_trade_no, //商户授权订单号
            "order_title" => "停车费",
            "product_code" => "PREAUTH_PAY",//产品码固定值
            'amount' => $amount, //订单金额，单位为元
            'deposit_product_mode' => 'POSTPAY',
            // "payee_user_id" => "2088721059075705",
            // "payee_logon_id" => '<EMAIL>'
        ];
        $request = Request::instance();

        // 授权异步回调地址
        $alipay_config['notify_url'] = $request->domain() . '/index/notify/alipay_preauth';

        // 发起请求
        try{
            $aop = new \Alipay\AlipayTradeService($alipay_config);
            $result = $aop->preAuthFreeze($bizContent);
            return $result;
        }catch(\Exception $e){
            return false;
        }
    }


    /**
     * 冻结资金转支付
     * @param string $appid 支付宝appid
     * @param string $alipay_public_key 支付宝公钥
     * @param string $app_private_key 支付宝私钥
     * @param string $out_trade_no 商户订单号
     * @param string $amount 订单金额
     * @param string $auth_no 授权号
     * @return string trade_no
     */
    public static function froZenTopay($appid,$alipay_public_key,$app_private_key,$out_trade_no,$amount,$auth_no)
    {
        // 构造支付宝配置
        $alipay_config = [
                'app_id' => $appid,
                'cert_mode' => 0,
                'alipay_public_key' => $alipay_public_key,
                'app_private_key' => $app_private_key,
                'sign_type' => "RSA2",
                'charset' => "UTF-8",
                // 'pageMethod' => '1',
                // 'gateway_url' => "https://openapi-sandbox.dl.alipaydev.com/gateway.do",
            ];

        //构造业务参数bizContent
        $bizContent = [
            'out_trade_no' => $out_trade_no, //商户订单号
            'total_amount' => $amount, //订单金额，单位为元
            "subject" => "停车费",
            "product_code" => "PREAUTH_PAY",//产品码固定值
            'auth_no' => $auth_no,
            "auth_confirm_mode" => "COMPLETE",
        ];

        $request = Request::instance();

        // 授权异步回调地址
        $alipay_config['notify_url'] = $request->domain() . '/index/notify/alipay_trade';
        
        // 发起支付请求
        try{
            $aop = new \Alipay\AlipayTradeService($alipay_config);
            $result = $aop->aopExecute('alipay.trade.pay', $bizContent);
            trace("froZenTopay success：".json_encode($result),'error');
            return ['success'=>true,'trade_no'=>$result['trade_no'],'msg'=>'成功'];
        }catch(\Exception $e){
            trace("froZenTopay error：".$e->getMessage(),'error');
            return ['success'=>false,'msg'=>$e->getMessage()];
        }
    }
    
    public static function unfreeze($appid,$alipay_public_key,$app_private_key,$out_trade_no,$amount,$auth_no)
    {
        // 构造支付宝配置
        $alipay_config = [
            'app_id' => $appid,
            'cert_mode' => 0,
            'alipay_public_key' => $alipay_public_key,
            'app_private_key' => $app_private_key,
            'sign_type' => "RSA2",
            'charset' => "UTF-8",
            // 'pageMethod' => '1',
            // 'gateway_url' => "https://openapi-sandbox.dl.alipaydev.com/gateway.do",
        ];

        //构造业务参数bizContent
        $bizContent = [
            'auth_no' => $auth_no,
            'out_request_no' => $out_trade_no, //商户订单号
            'amount' => $amount, //订单金额，单位为元
            "remark" => "扣款失败退还押金"
        ];

        $request = Request::instance();

        // 授权异步回调地址
        $alipay_config['notify_url'] = $request->domain() . '/index/notify/alipay_trade';
        
        // 发起支付请求
        try{
            $aop = new \Alipay\AlipayTradeService($alipay_config);
            $result = $aop->aopExecute('alipay.fund.auth.order.unfreeze', $bizContent);
            return ['success'=>true,'trade_no'=>$result['trade_no']];
        }catch(\Exception $e){
            return ['success'=>false,'msg'=>$e->getMessage()];
        }
    }
}