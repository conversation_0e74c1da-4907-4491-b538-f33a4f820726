<?php

namespace app\admin\controller;

use app\admin\model\Admin;
use app\admin\model\aweme\Account;
use app\admin\model\Order;
use app\common\controller\Backend;
use fast\Date;

/**
 * 控制台
 *
 * @icon   fa fa-dashboard
 * @remark 用于展示当前系统中的统计数据、统计报表及重要实时数据
 */
class Dashboard extends Backend
{

    /**
     * 查看
     */
    public function index()
    {
        try {
            \think\Db::execute("SET @@sql_mode='';");
        } catch (\Exception $e) {

        }
        $where = [];

        if (in_array($this->auth->id, [1,2])) {
        } else {
            $where = ['admin_id' => $this->auth->id];
        }

        // 总体数据统计
        $today_data = (new \app\admin\model\Order)->where($where)
            ->whereTime('create_time', 'today')
            ->field('admin_id,ROUND(sum(IF(pay_status="1",amount,0)),2) as amount,sum(IF(pay_status="0",amount,0)) as amount_all,count(IF(pay_status="1",1,null)) as c1,count(IF(admin_id>0,1,null)) as c2')
            ->select();
        // 今日充值金额
        $topUpToday = $today_data[0]->amount ? "¥".$today_data[0]->amount:"¥0";
        // 今日充值成率
        $recharge_ratio = $today_data[0]->amount ? round($today_data[0]->c1 / $today_data[0]->c2,4) * 100 . '%' : '0%';

        // 支付宝点单码数据统计 (channel_code = 9008)
        $diandan_today_data = (new \app\admin\model\Order)->where($where)
            ->where('channel_code', '9008')
            ->whereTime('create_time', 'today')
            ->field('ROUND(sum(IF(pay_status="1",amount,0)),2) as amount,count(IF(pay_status="1",1,null)) as c1,count(IF(admin_id>0,1,null)) as c2')
            ->find();
        $diandan_amount = $diandan_today_data->amount ? "¥".$diandan_today_data->amount : "¥0";
        $diandan_ratio = $diandan_today_data->c2 > 0 ? round($diandan_today_data->c1 / $diandan_today_data->c2, 4) * 100 . '%' : '0%';

        // 支付宝商家码数据统计 (channel_code = 9009)
        $shopcode_today_data = (new \app\admin\model\Order)->where($where)
            ->where('channel_code', '9009')
            ->whereTime('create_time', 'today')
            ->field('ROUND(sum(IF(pay_status="1",amount,0)),2) as amount,count(IF(pay_status="1",1,null)) as c1,count(IF(admin_id>0,1,null)) as c2')
            ->find();
        $shopcode_amount = $shopcode_today_data->amount ? "¥".$shopcode_today_data->amount : "¥0";
        $shopcode_ratio = $shopcode_today_data->c2 > 0 ? round($shopcode_today_data->c1 / $shopcode_today_data->c2, 4) * 100 . '%' : '0%';

        // 支付宝点单码账号统计
        $diandan_normal_count = db('account')->where($where)->where('channel', '9008')->where('status', 'normal')->count();
        $diandan_abnormal_count = db('account')->where($where)->where('channel', '9008')
            ->where(function($query) {
                $query->where('status', 'hidden')
                      ->whereOr('statusinfo', 'like', '%异常%');
            })->count();

        // 支付宝商家码账号统计
        $shopcode_normal_count = db('account')->where($where)->where('channel', '9009')->where('status', 'normal')->count();
        $shopcode_abnormal_count = db('account')->where($where)->where('channel', '9009')
            ->where(function($query) {
                $query->where('status', 'hidden')
                      ->whereOr('statusinfo', 'like', '%异常%');
            })->count();

        // 总体账号统计
        $normal_count = db('account')->where($where)->where('status', 'normal')->count();
        $abnormal_count = db('account')->where($where)
            ->where(function($query) {
                $query->where('status', 'hidden')
                      ->whereOr('statusinfo', 'like', '%异常%');
            })->count();

        // 每日充值统计
        $recharge_list = (new \app\admin\model\Order)->where($where)->where('pay_status', '1')
            ->group('FROM_UNIXTIME(create_time, "%Y-%m-%d")')
            ->whereTime('create_time', '-7 days')
            ->field('FROM_UNIXTIME(create_time, "%Y-%m-%d") as date,ROUND(sum(amount),2) as amount')
            ->select();

        // 支付宝点单码7天充值统计
        $diandan_recharge_list = (new \app\admin\model\Order)->where($where)->where('pay_status', '1')
            ->where('channel_code', '9008')
            ->group('FROM_UNIXTIME(create_time, "%Y-%m-%d")')
            ->whereTime('create_time', '-7 days')
            ->field('FROM_UNIXTIME(create_time, "%Y-%m-%d") as date,ROUND(sum(amount),2) as amount')
            ->select();

        // 支付宝商家码7天充值统计
        $shopcode_recharge_list = (new \app\admin\model\Order)->where($where)->where('pay_status', '1')
            ->where('channel_code', '9009')
            ->group('FROM_UNIXTIME(create_time, "%Y-%m-%d")')
            ->whereTime('create_time', '-7 days')
            ->field('FROM_UNIXTIME(create_time, "%Y-%m-%d") as date,ROUND(sum(amount),2) as amount')
            ->select();

        $this->view->assign([
            'recharge_list'           => $recharge_list,
            'normal_count'            => $normal_count,
            'abnormal_count'          => $abnormal_count,
            'today_amount'            => $topUpToday,
            'recharge_ratio'          => $recharge_ratio,
            // 支付宝点单码数据
            'diandan_amount'          => $diandan_amount,
            'diandan_ratio'           => $diandan_ratio,
            'diandan_normal_count'    => $diandan_normal_count,
            'diandan_abnormal_count'  => $diandan_abnormal_count,
            'diandan_recharge_list'   => $diandan_recharge_list,
            // 支付宝商家码数据
            'shopcode_amount'         => $shopcode_amount,
            'shopcode_ratio'          => $shopcode_ratio,
            'shopcode_normal_count'   => $shopcode_normal_count,
            'shopcode_abnormal_count' => $shopcode_abnormal_count,
            'shopcode_recharge_list'  => $shopcode_recharge_list,
        ]);

        return $this->view->fetch();
    }

}
