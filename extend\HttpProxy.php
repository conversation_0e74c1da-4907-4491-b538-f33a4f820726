<?php
/**
 * 第三方代理池
 */

use fast\Http;

class HttpProxy
{

    /**
     * 神龙代理
     * @param string $area     城市代码
     * @return mixed|json
     */
    public static function shenlong($area, $key, $sign)
    {
        $url = "http://api.shenlongip.com/ip";
        $params = [
            "key"=>$key,
            "pattern"=>"json",
            "count"=>"1", // 提取数量, 不能超过200，默认值10；
            "area"=>$area,
            "mr"=>"2", // ip去重,默认选择去重。
            "protocol"=>"1",
            "sign"=>$sign
        ];
        $data = Http::get($url,$params);
        if (empty($data)) {
            return json_decode(json_encode(['code'=>404,'msg'=>'获取代理失败']));
        }
        $jsonData = json_decode(trim($data));
        try {
            $ip = $jsonData->data[0]->ip;
            $port = $jsonData->data[0]->port;
        } catch (Exception $e) {
            trace('shenglong:'.$data,'error');
            return json_decode(json_encode(['code'=>206,'msg'=>'暂无可用ip']));
        }
        if ($ip == '' || $port == '') {
            return json_decode(json_encode(['code'=>206,'msg'=>'暂无可用ip']));
        }
        return $jsonData;
    }

    /**
     * 小熊代理
     * @param string $area     城市代码
     * @return mixed|json
     */
    public static function xiaoxiong($area, $key)
    {
        $url = "http://find.xiaoxiongcloud.com/find_http";
        $params = [
            "key"=>$key,
            "type"=>"json",
            "count"=>"1",
            "city"=>$area,
            "pw"=>"no"
        ];
        $data = Http::get($url,$params);
        if (empty($data)) {
            return json_decode(json_encode(['code'=>404,'msg'=>'获取代理失败']));
        }
        $jsonData = json_decode(trim($data));
        try {
            $ip = $jsonData->list[0]->sever;
            $port = $jsonData->list[0]->port;
        } catch (Exception $e) {
            trace('xiaoxiong:'.$data,'error');
            return json_decode(json_encode(['code'=>404,'msg'=>'获取代理失败']));
        }
        if ($ip == '' || $port == '') {
            return json_decode(json_encode(['code'=>404,'msg'=>'获取代理失败']));
        }
        return json_decode(json_encode(['code'=>200, "data"=>[["ip"=>$ip,"port"=>$port]]]));
    }

    public static function get($area)
    {
        try {
            $data = self::xiaoxiong($area, "daecb6136ef78250");
            if ($data->code == 200) {
                return $data;
            }

//            $data = self::shenlong($area, "5hu19azi", "0160fa5f5b85b6c41b2718912b7b3988");
//            if ($data->code == 200) {
//                return $data;
//            }

            return $data;
        } catch (Exception $e) {
            trace('get proxy error:'.$e, 'error');
            return json_decode(json_encode(['code'=>404,'msg'=>'获取代理失败']));
        }

    }
}