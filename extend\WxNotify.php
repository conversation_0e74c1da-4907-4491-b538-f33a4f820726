<?php


class WxNotify
{
    public static function notify($key)
    {
        // 获取回调数据
        $xml = file_get_contents('php://input');
        $data = WxNotify::fromXml($xml);

        // 验证签名
        $sign = $data['sign'];
        unset($data['sign']);
        if (WxNotify::makeSign($data, $key) != $sign) {
            return ['status'=>'error','data'=>$data];
        }

        // 处理业务逻辑
        if ($data['result_code'] == 'SUCCESS' && $data['return_code'] == 'SUCCESS') {
            // 支付成功，处理订单逻辑
            return ['status'=>'success','data'=>$data];
//            exit('<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>');
        } else {
            return ['status'=>'error','data'=>$data];
            // 支付失败，记录日志
            // ...
            // 返回结果
//            exit('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[ERROR]]></return_msg></xml>');
        }
    }

    // 生成签名
    public static function makeSign($data, $key) {
        ksort($data);
        $str = '';
        foreach ($data as $k => $v) {
            if ($v != '' && !is_array($v)) {
                $str .= $k.'='.$v.'&';
            }
        }
        $str .= 'key='.$key;
        return strtoupper(md5($str));
    }

    // xml转数组
    public static function fromXml($xml) {
        libxml_disable_entity_loader(true);
        return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    }
}