<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0 user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <title>直营商城</title>
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/Mao.min.css">
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/style.css">
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/Mao.diy.css">
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/iconfont.css">
    <script src="__CDN__/shop/jquery-2.1.1.min.js"></script>
    <script src="__CDN__/shop/layer1.js"></script>
    <link rel="stylesheet" type="text/css" href="__CDN__/shop/index.css">
    <script src="__CDN__/shop/zepto.js"></script>
    <script src="__CDN__/shop/bui.js"></script>
    <script src="__CDN__/shop/isPc.js"></script>
    <script src="__CDN__/shop/Mao.js"></script>
    <style>
        .bui-list-pic .bui-pic img,
        .bui-list-photo .bui-thumbnail img {
            width: 100%;
            height: 175px;
        }

        .bui-nav>.active {
            color: #333;
        }

        .bui-nav>[class*=bui-btn].active:after {
            background: #333;
        }

        [class*=bui-btn] .bui-thumbnail {
            width: 1.2rem;
        }

        .bui-rating .bui-rating-cell {
            font-size: .2rem;
        }

        .bui-rating .bui-rating-cell-half,
        .bui-rating .bui-rating-cell-full {
            color: #ff0057 !important;
        }

        .icon_content {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #666;
        }

        .icon_item {
            display: flex;
            align-items: center;
        }

        .icon_item img {
            width: 18px;
            height: 18px;
            margin-right: 1px;
        }

        .icon_item:last-child {
            margin-left: 10px;
        }

        .bui-rating .bui-rating-cell {
            font-size: .16rem;
        }
        .dj{
            background: rgb(254,244,243);
            color: #ff0057;
            padding: 4px;
            font-size: 6px;
        }
        .tj{
            background: #fff;
            font-weight:500;
            padding: 5px;
        }
        .tj div{
            width: fit-content;
            border-left: 5px solid red;
            padding-left: 3px;
        }
        .tx{
            position: relative;
            z-index: 10;
            overflow: hidden;
            width:50px;
            height:50px;
            float: left;
            border-radius: 10px;
        }
        .span1{
            padding-left: 60px;
        }

        .bui-rating .bui-rating-cell {
            position: relative;
            font-family: "icon";
            -webkit-text-stroke-width: 0.1px;
            -webkit-font-smoothing: antialiased;
            margin: 0 .05rem;
            display: inline-block;
            -webkit-appearance: none;
            background-color: transparent;
            border: 0;
            outline: 0;
            background-size: 100% auto;
            color: #aaa;
            font-size: .48rem
        }

        .bui-rating .bui-rating-cell:before {
            display: inline-block;
            content: "\e62a";
            line-height: 1;
            border-radius: 0
        }

        .bui-rating .bui-rating-cell:checked {
            color: #fc3
        }

        .bui-rating .bui-rating-cell:checked:before {
            display: inline-block;
            content: "\e629";
            line-height: 1;
            border-radius: 0
        }

        .bui-rating .bui-rating-cell .bui-rating-cell-full {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 2;
            overflow: hidden
        }

        .bui-rating .bui-rating-cell-half,.bui-rating .bui-rating-cell-full {
            -webkit-appearance: none;
            background-color: transparent;
            color: #fc3
        }

        .bui-rating .bui-rating-cell-half:before,.bui-rating .bui-rating-cell-full:before {
            content: "\e629";
            line-height: 1;
            border-radius: 0
        }

        .bui-rating .bui-rating-cell-half:before {
            content: "\e601"
        }

        .bui-rating.mini .bui-rating-cell {
            font-size: .2rem
        }

        .bui-rating.small .bui-rating-cell {
            font-size: .36rem
        }

        .bui-rating.large .bui-rating-cell {
            font-size: .6rem
        }

        .bui-rating.xlarge .bui-rating-cell {
            font-size: .76rem
        }

    </style>

</head>
<body>
<div class="fui-page-group">
    <div class="fui-page  fui-page-current " style="top: 0; background-color: #fafafa;">
        <div class="fui-content navbar" style="background-color: #f3f3f3; padding-bottom: 0;">
            <div class="fui-notice" style="background: #ffffff; border-color: ; margin-bottom: 0px;" data-speed="4">
                <ul class="bui-list bui-list-thumbnail">
                    <li class="bui-btn bui-box">
                        <div class="tx"><img width="100%" height="100%" src="__CDN__/shop/logo.jpg" alt=""></div>
                        <div class="span1">
                            <h3 class="item-title" style="font-weight: bolder;font-size:14px;color:#333;margin-bottom: 0.1rem;">直营商城</h3>
                            <div class="item-text" style="display:flex;justify-content: space-between">
                                <div style="color: #ff0057;font-size:13px;float:left">5.0<span class="dj">高</span>
                                    <div id="rating" class="bui-rating" style="float:left"></div>
                                </div>

                                <p style="color:#666;font-size:12px;padding:2px;">已售1万</p>
                            </div>
                            <div class="icon_content">
                                <div class="icon_item"><img src="__CDN__/shop/zijin.png"> <span>已缴纳保证金</span> </div>
                                <div class="icon_item"><img src="__CDN__/shop/zizhi.png"> <span>商家资质</span> </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="fui-cell-group">
                <div class="fui-cell">
                    <div class="fui-cell-info" style="border-left: 5px solid #fb375b;padding-left: 5px;">
                        最新商品
                        <a style="float:right" href="{:url('/index/index/list')}">更多</a>
                    </div>
                </div>
                <hr>
                <div class="fui-goods-group block three" style="background: ;">
                    <a class="fui-goods-item" style="position: relative;" href="{:url('/index/index/goods',['id' => 1])}">
                        <div class="image triangle" data-text="" style="background-image: url('https://cbu01.alicdn.com/img/ibank/O1CN01G7jrWK1rZutB5kl8V_!!*************-0-cib.jpg');"></div>
                        <div class="detail">
                            <div class="name" style="color: #262626;">
                                港式金汤鲍鱼花胶鸡加热即食鱼胶滋补食材1650g礼盒                          </div>
                            <div class="price">
									<span class="text" style="color: #ed2822;">
	                                    <p class="minprice">¥ 0.01</p>
	                                </span>
                                <span class="buy buybtn-3" style="background-color: #01a1ff;margin-right: 5px;">邮</span>                                    <span class="buy buybtn-3" style="background-color: #fe5455;"><i class="icon icon-cartfill"></i></span>
                            </div>
                        </div>
                    </a>
                    <a class="fui-goods-item" style="position: relative;" href="{:url('/index/index/goods',['id' => 1])}">
                        <div class="image triangle" data-text="" style="background-image: url('https://cbu01.alicdn.com/img/ibank/2020/248/667/21384766842_493672081.jpg');"></div>
                        <div class="detail">
                            <div class="name" style="color: #262626;">
                                土耳其海参干货批发 野生希腊黑海参土耳其淡干海参                          </div>
                            <div class="price">
									<span class="text" style="color: #ed2822;">
	                                    <p class="minprice">¥ 0.01</p>
	                                </span>
                                <span class="buy buybtn-3" style="background-color: #01a1ff;margin-right: 5px;">邮</span>                                    <span class="buy buybtn-3" style="background-color: #fe5455;"><i class="icon icon-cartfill"></i></span>
                            </div>
                        </div>
                    </a>
                    <a class="fui-goods-item" style="position: relative;" href="{:url('/index/index/goods',['id' => 1])}">
                        <div class="image triangle" data-text="" style="background-image: url('https://cbu01.alicdn.com/img/ibank/O1CN01dx51001rZutH59CkV_!!*************-0-cib.jpg');"></div>
                        <div class="detail">
                            <div class="name" style="color: #262626;">
                                佛跳墙海鲜鲍鱼花胶海参干贝加热即食大盆菜1.5KG节日礼盒装送礼                          </div>
                            <div class="price">
									<span class="text" style="color: #ed2822;">
	                                    <p class="minprice">¥ 188.00</p>
	                                </span>
                                <span class="buy buybtn-3" style="background-color: #01a1ff;margin-right: 5px;">邮</span>                                    <span class="buy buybtn-3" style="background-color: #fe5455;"><i class="icon icon-cartfill"></i></span>
                            </div>
                        </div>
                    </a>
                    <a class="fui-goods-item" style="position: relative;" href="{:url('/index/index/goods',['id' => 1])}">
                        <div class="image triangle" data-text="" style="background-image: url('https://cbu01.alicdn.com/img/ibank/O1CN01dx51001rZutH59CkV_!!*************-0-cib.jpg');"></div>
                        <div class="detail">
                            <div class="name" style="color: #262626;">
                                佛跳墙海鲜鲍鱼花胶海参干贝加热即食大盆菜1.5KG节日礼盒装送礼                          </div>
                            <div class="price">
									<span class="text" style="color: #ed2822;">
	                                    <p class="minprice">¥ 1888.00</p>
	                                </span>
                                <span class="buy buybtn-3" style="background-color: #01a1ff;margin-right: 5px;">邮</span>                                    <span class="buy buybtn-3" style="background-color: #fe5455;"><i class="icon icon-cartfill"></i></span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>


        </div>
    </div>
    <div class="fui-navbar">
        <a href="{:url('/')}" class="external nav-item active">
            <span class="icon icon-home"></span>
            <span class="label">首页</span>
        </a>
        <a href="{:url('/index/index/list')}" class="external nav-item ">
            <span class="icon icon-list"></span>
            <span class="label">全部商品</span>
        </a>
        <a href="{:url('/index/index/ordersearch')}" class="external nav-item ">
            <span class="icon icon-daifukuan1"></span>
            <span class="label">订单查询</span>
        </a>
    </div>
</div>
<script>
    window.uiRating = bui.rating({
        id: "#rating",
        value: 5
    });

    // 展示
    var uiRating3 = bui.rating({
        id: "#rating3",
        disabled: true,
        value: 4
    });

    function goPAGE() {
        if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
            /*window.location.href="你的手机版地址";*/
        }
        else {
            /*window.location.href="你的电脑版地址";    */
            // window.location.href="isPc.html";
            document.getElementsByTagName("body")[0].style.width = '414px';
            document.getElementsByTagName("body")[0].style.margin = '0 auto';
            document.getElementsByTagName("footer")[0].style.width = '414px';

        }
    }
    goPAGE();

</script>
</body>
</html>