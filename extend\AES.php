<?php

class AES
{
    /**
     * [encrypt aes加密]
     * @param    [type]                   $sStr [要加密的数据]
     * @param    [type]                   $sKey [加密key]
     * @return   [type]                         [加密后的数据]
     */

    public static function encrypt($input, $key)
    {
        $data = openssl_encrypt($input, 'AES-128-ECB', $key, OPENSSL_RAW_DATA);
        $data = base64_encode($data);
        return $data;
    }
    /**
     * [decrypt aes解密]
     * @param    [type]                   $sStr [要解密的数据]
     * @param    [type]                   $sKey [加密key]
     * @return   [type]                         [解密后的数据]
     */
    public static function decrypt($sStr, $sKey)
    {
        $decrypted = openssl_decrypt(base64_decode($sStr), 'AES-128-ECB', $sKey, OPENSSL_RAW_DATA);
        return $decrypted;
    }

    /**
     * aes解密
     */
    public static function getAES($data ,$SK)
    {
//        $key = substr(openssl_digest(openssl_digest($SK, 'sha1', true), 'sha1', true), 0, 16);
        $decrypted = openssl_decrypt(hex2bin($data), 'AES-128-ECB', $SK, OPENSSL_RAW_DATA);
        return $decrypted;
    }
}